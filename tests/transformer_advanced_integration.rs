//! Advanced integration tests for Transformer models.
//! 
//! Tests complex scenarios like multi-layer stacking, encoder-decoder interactions,
//! attention masking, and performance characteristics.

use qilin_inference::tensor::{CpuTensor, Shape, cpu::CpuTensorFactory};
use qilin_inference::layers::ParameterInit;
use qilin_inference::transformer::{
    config::{TransformerConfig, ModelType, ActivationType, NormalizationType, PositionEncodingType},
    block::{TransformerBlock, BlockType},
    encoder::TransformerEncoder,
    decoder::TransformerDecoder,
    position::{utils::{create_causal_mask, create_padding_mask}, PositionEncodingConfig, create_position_encoding},
    variants::{GPTModel, GPTConfig, BERTModel, BERTConfig, T5Model, T5Config},
};

/// Test deep multi-layer encoder stacking.
#[test]
fn test_deep_encoder_stacking() {
    // Create a deep encoder configuration (24 layers like BERT-Large)
    let mut config = TransformerConfig::bert_base();
    config.num_layers = 24;
    config.hidden_size = 1024;
    config.num_attention_heads = 16;
    config.intermediate_size = 4096;
    
    let mut encoder = TransformerEncoder::<f32>::new(&config).unwrap();
    encoder.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    let batch_size = 2;
    let seq_len = 32;
    let hidden_size = config.hidden_size;
    
    let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 0.1).unwrap();
    let output = encoder.forward(&input, None).unwrap();
    
    // Check that deep stacking works
    assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    assert_eq!(encoder.num_layers(), 24);
    
    // Check that output is different from input (transformation occurred)
    let input_data = input.data();
    let output_data = output.data();
    let mut different_count = 0;
    for (i, o) in input_data.iter().zip(output_data.iter()) {
        if (i - o).abs() > 1e-6 {
            different_count += 1;
        }
    }
    assert!(different_count > input_data.len() / 2, "Deep encoder should significantly transform input");
    
    // Check that values remain reasonable (no explosion/vanishing)
    for &value in output_data {
        assert!(value.is_finite(), "Output should be finite");
        assert!(value.abs() < 100.0, "Output should not explode");
        assert!(value.abs() > 1e-6, "Output should not vanish completely");
    }
}

/// Test deep multi-layer decoder stacking with causal masking.
#[test]
fn test_deep_decoder_stacking_with_masking() {
    // Create a deep decoder configuration (48 layers like GPT-3 style)
    let mut config = TransformerConfig::gpt2_large();
    config.num_layers = 24; // Reduced for testing
    
    let mut decoder = TransformerDecoder::<f32>::new(&config).unwrap();
    decoder.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    let batch_size = 2;
    let seq_len = 16;
    let hidden_size = config.hidden_size;
    
    let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 0.1).unwrap();
    
    // Create causal mask
    let causal_mask = create_causal_mask::<f32>(seq_len).unwrap();
    
    let output = decoder.forward(&input, None, Some(&causal_mask), None).unwrap();
    
    // Check that deep decoder stacking works
    assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    assert_eq!(decoder.num_layers(), 24);
    assert!(decoder.is_decoder_only());
    
    // Check output properties
    let output_data = output.data();
    for &value in output_data {
        assert!(value.is_finite(), "Decoder output should be finite");
        assert!(value.abs() < 50.0, "Decoder output should not explode");
    }
}

/// Test encoder-decoder interaction with different sequence lengths.
#[test]
fn test_encoder_decoder_different_lengths() {
    let config = TransformerConfig::t5_base();
    let mut block = TransformerBlock::<f32>::new(config.clone()).unwrap();
    block.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    let batch_size = 3;
    let encoder_seq_len = 20;
    let decoder_seq_len = 15;
    let hidden_size = config.hidden_size;
    
    // Create inputs with different sequence lengths
    let encoder_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, encoder_seq_len, hidden_size]), 0.0, 0.1).unwrap();
    let decoder_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, decoder_seq_len, hidden_size]), 0.0, 0.1).unwrap();
    
    // Create attention masks
    let encoder_mask_data = vec![1; batch_size * encoder_seq_len];
    let encoder_mask = CpuTensor::from_data(encoder_mask_data, Shape::new(vec![batch_size, encoder_seq_len])).unwrap();
    let encoder_attention_mask = create_padding_mask::<f32>(&encoder_mask).unwrap();
    
    let decoder_causal_mask = create_causal_mask::<f32>(decoder_seq_len).unwrap();
    
    // Test encoder-decoder forward pass
    let output = block.forward(
        &decoder_input,
        Some(&encoder_input),
        Some(&decoder_causal_mask),
        Some(&encoder_attention_mask),
    ).unwrap();
    
    assert_eq!(output.shape().dims(), &[batch_size, decoder_seq_len, hidden_size]);
    
    // Test separate encode and decode operations
    let encoded = block.encode(&encoder_input, Some(&encoder_attention_mask)).unwrap();
    assert_eq!(encoded.shape().dims(), &[batch_size, encoder_seq_len, hidden_size]);
    
    let decoded = block.decode(
        &decoder_input,
        Some(&encoded),
        Some(&decoder_causal_mask),
        Some(&encoder_attention_mask),
    ).unwrap();
    assert_eq!(decoded.shape().dims(), &[batch_size, decoder_seq_len, hidden_size]);
}

/// Test attention masking effectiveness.
#[test]
fn test_attention_masking_effectiveness() {
    let config = TransformerConfig::gpt2_small();
    let mut model = GPTModel::<f32>::new(config).unwrap();
    model.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    let batch_size = 2;
    let seq_len = 8;
    
    // Create input with padding
    let input_data = vec![
        // First sequence: [1, 2, 3, 4, 0, 0, 0, 0] (4 real tokens, 4 padding)
        1, 2, 3, 4, 0, 0, 0, 0,
        // Second sequence: [5, 6, 7, 8, 9, 10, 0, 0] (6 real tokens, 2 padding)
        5, 6, 7, 8, 9, 10, 0, 0,
    ];
    let input_ids = CpuTensor::from_data(input_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    // Create attention mask (1 for real tokens, 0 for padding)
    let mask_data = vec![
        1, 1, 1, 1, 0, 0, 0, 0,  // First sequence
        1, 1, 1, 1, 1, 1, 0, 0,  // Second sequence
    ];
    let attention_mask = CpuTensor::from_data(mask_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    // Forward pass with mask
    let output_with_mask = model.forward(&input_ids, Some(&attention_mask)).unwrap();
    
    // Forward pass without mask
    let output_without_mask = model.forward(&input_ids, None).unwrap();
    
    // Outputs should have the same shape
    assert_eq!(output_with_mask.shape(), output_without_mask.shape());
    
    // But the values should be different due to masking
    let with_mask_data = output_with_mask.data();
    let without_mask_data = output_without_mask.data();
    
    let mut different_count = 0;
    for (with, without) in with_mask_data.iter().zip(without_mask_data.iter()) {
        if (with - without).abs() > 1e-6 {
            different_count += 1;
        }
    }
    
    assert!(different_count > 0, "Masking should affect the output");
}

/// Test position encoding effectiveness.
#[test]
fn test_position_encoding_effectiveness() {
    let max_length = 50;
    let d_model = 256;
    
    // Test different position encoding types
    let learned_config = PositionEncodingConfig::new(PositionEncodingType::Learned, max_length, d_model);
    let sin_config = PositionEncodingConfig::new(PositionEncodingType::Sinusoidal, max_length, d_model);
    let rope_config = PositionEncodingConfig::new(PositionEncodingType::RoPE, max_length, d_model);
    
    let learned_pos = create_position_encoding::<f32>(&learned_config).unwrap();
    let sin_pos = create_position_encoding::<f32>(&sin_config).unwrap();
    let rope_pos = create_position_encoding::<f32>(&rope_config).unwrap();
    
    let batch_size = 2;
    let seq_len = 20;
    let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0).unwrap();
    
    // Apply different position encodings
    let learned_output = learned_pos.encode(&input, None).unwrap();
    let sin_output = sin_pos.encode(&input, None).unwrap();
    let rope_output = rope_pos.encode(&input, None).unwrap();
    
    // All should have the same shape
    assert_eq!(learned_output.shape(), input.shape());
    assert_eq!(sin_output.shape(), input.shape());
    assert_eq!(rope_output.shape(), input.shape());
    
    // But different values (except RoPE which might return input unchanged in this simple test)
    let input_data = input.data();
    let learned_data = learned_output.data();
    let sin_data = sin_output.data();
    
    // Learned and sinusoidal should modify the input
    let mut learned_different = 0;
    let mut sin_different = 0;
    
    for ((inp, learned), sin) in input_data.iter().zip(learned_data.iter()).zip(sin_data.iter()) {
        if (inp - learned).abs() > 1e-6 {
            learned_different += 1;
        }
        if (inp - sin).abs() > 1e-6 {
            sin_different += 1;
        }
    }
    
    assert!(learned_different > 0, "Learned position encoding should modify input");
    assert!(sin_different > 0, "Sinusoidal position encoding should modify input");
}

/// Test model configuration validation.
#[test]
fn test_configuration_validation() {
    // Test valid configurations
    let valid_config = TransformerConfig::new(ModelType::DecoderOnly, 1000, 512, 6, 8);
    assert!(valid_config.validate().is_ok());
    
    // Test invalid configurations
    let mut invalid_config = valid_config.clone();
    invalid_config.hidden_size = 513; // Not divisible by num_attention_heads
    assert!(invalid_config.validate().is_err());
    
    invalid_config = valid_config.clone();
    invalid_config.num_attention_heads = 0; // Zero heads
    assert!(invalid_config.validate().is_err());
    
    invalid_config = valid_config.clone();
    invalid_config.num_layers = 0; // Zero layers
    assert!(invalid_config.validate().is_err());
    
    invalid_config = valid_config.clone();
    invalid_config.vocab_size = 0; // Zero vocab size
    assert!(invalid_config.validate().is_err());
}

/// Test memory efficiency with large models.
#[test]
fn test_memory_efficiency() {
    // Create a moderately large model to test memory usage
    let config = TransformerConfig::new(ModelType::DecoderOnly, 10000, 1024, 12, 16);
    let mut model = GPTModel::<f32>::new(GPTConfig { base_config: config, ..GPTConfig::gpt2_small() }).unwrap();
    model.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    let batch_size = 4;
    let seq_len = 64;
    
    // Create input
    let mut input_data = Vec::with_capacity(batch_size * seq_len);
    for _ in 0..(batch_size * seq_len) {
        input_data.push(rand::random::<usize>() % 10000);
    }
    let input_ids = CpuTensor::from_data(input_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    // Multiple forward passes to test memory stability
    for i in 0..5 {
        let output = model.forward(&input_ids, None).unwrap();
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, 10000]);
        
        // Check that outputs remain stable across iterations
        let output_data = output.data();
        for &value in output_data {
            assert!(value.is_finite(), "Output should remain finite in iteration {}", i);
            assert!(value.abs() < 1000.0, "Output should not explode in iteration {}", i);
        }
    }
}

/// Test gradient flow simulation (simplified).
#[test]
fn test_gradient_flow_simulation() {
    let config = TransformerConfig::gpt2_small();
    let mut encoder = TransformerEncoder::<f32>::new(&config).unwrap();
    encoder.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    let batch_size = 2;
    let seq_len = 16;
    let hidden_size = config.hidden_size;
    
    // Create input with small perturbation
    let input1 = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 0.1).unwrap();
    let mut input2_data = input1.data().clone();
    
    // Add small perturbation to first element
    input2_data[0] += 1e-6;
    let input2 = CpuTensor::from_data(input2_data, input1.shape().clone()).unwrap();
    
    // Forward passes
    let output1 = encoder.forward(&input1, None).unwrap();
    let output2 = encoder.forward(&input2, None).unwrap();
    
    // Check that small input changes lead to small output changes (stability)
    let output1_data = output1.data();
    let output2_data = output2.data();
    
    let mut max_diff = 0.0f32;
    for (o1, o2) in output1_data.iter().zip(output2_data.iter()) {
        let diff = (o1 - o2).abs();
        max_diff = max_diff.max(diff);
    }
    
    // The difference should be small but non-zero (indicating gradient flow)
    assert!(max_diff > 1e-9, "Model should be sensitive to input changes");
    assert!(max_diff < 1e-3, "Model should be stable to small input changes");
}

/// Test different activation functions.
#[test]
fn test_different_activation_functions() {
    let activations = vec![
        ActivationType::ReLU,
        ActivationType::GELU,
        ActivationType::Swish,
        ActivationType::Tanh,
    ];
    
    for activation in activations {
        let mut config = TransformerConfig::gpt2_small();
        config.activation_function = activation;
        config.num_layers = 2; // Reduce for faster testing
        
        let mut model = GPTModel::<f32>::new(GPTConfig { base_config: config, ..GPTConfig::gpt2_small() }).unwrap();
        model.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        let batch_size = 1;
        let seq_len = 8;
        let input_data = vec![1, 2, 3, 4, 5, 6, 7, 8];
        let input_ids = CpuTensor::from_data(input_data, Shape::new(vec![batch_size, seq_len])).unwrap();
        
        let output = model.forward(&input_ids, None).unwrap();
        
        // Check that model works with different activations
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, 50257]);
        
        let output_data = output.data();
        for &value in output_data {
            assert!(value.is_finite(), "Output should be finite with {:?} activation", activation);
        }
    }
}
