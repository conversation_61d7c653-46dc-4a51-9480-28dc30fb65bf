//! Integration tests for Transformer models.

use qilin_inference::tensor::{CpuTensor, <PERSON>ha<PERSON>, cpu::CpuTensorFactory};
use qilin_inference::layers::ParameterInit;
use qilin_inference::transformer::{
    config::TransformerConfig,
    variants::{GPTModel, GPTConfig, BERTModel, BERTConfig, T5Model, T5Config},
};

/// Test complete GPT model pipeline.
#[test]
fn test_gpt_model_integration() {
    let config = GPTConfig::gpt2_small();
    let mut model = GPTModel::<f32>::new(config).unwrap();
    model.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    // Test forward pass with token IDs
    let batch_size = 2;
    let seq_len = 10;
    let vocab_size = 50257;
    
    // Create input token IDs (random valid token IDs)
    let mut input_data = Vec::with_capacity(batch_size * seq_len);
    for _ in 0..(batch_size * seq_len) {
        input_data.push((rand::random::<usize>() % vocab_size).min(vocab_size - 1));
    }
    let input_ids = CpuTensor::from_data(input_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    // Forward pass
    let logits = model.forward(&input_ids, None).unwrap();
    
    // Check output shape: [batch_size, seq_len, vocab_size]
    assert_eq!(logits.shape().dims(), &[batch_size, seq_len, vocab_size]);
    
    // Check that logits are finite
    let logits_data = logits.data();
    for &value in logits_data {
        assert!(value.is_finite(), "Logits should be finite");
    }
    
    // Test with attention mask
    let attention_mask_data = vec![1; batch_size * seq_len];
    let attention_mask = CpuTensor::from_data(attention_mask_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    let logits_with_mask = model.forward(&input_ids, Some(&attention_mask)).unwrap();
    assert_eq!(logits_with_mask.shape().dims(), &[batch_size, seq_len, vocab_size]);
}

/// Test complete BERT model pipeline.
#[test]
fn test_bert_model_integration() {
    let config = BERTConfig::bert_base();
    let mut model = BERTModel::<f32>::new(config).unwrap();
    model.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    // Test forward pass with token IDs
    let batch_size = 2;
    let seq_len = 16;
    let vocab_size = 30522;
    let hidden_size = 768;
    
    // Create input token IDs (including [CLS] and [SEP] tokens)
    let mut input_data = Vec::with_capacity(batch_size * seq_len);
    for i in 0..(batch_size * seq_len) {
        if i % seq_len == 0 {
            input_data.push(101); // [CLS] token
        } else if i % seq_len == seq_len - 1 {
            input_data.push(102); // [SEP] token
        } else {
            input_data.push((rand::random::<usize>() % (vocab_size - 200)) + 200); // Regular tokens
        }
    }
    let input_ids = CpuTensor::from_data(input_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    // Create token type IDs (all zeros for single sentence)
    let token_type_data = vec![0; batch_size * seq_len];
    let token_type_ids = CpuTensor::from_data(token_type_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    // Create attention mask (all ones - no padding)
    let attention_mask_data = vec![1; batch_size * seq_len];
    let attention_mask = CpuTensor::from_data(attention_mask_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    // Forward pass
    let outputs = model.forward(&input_ids, Some(&token_type_ids), Some(&attention_mask), None).unwrap();
    
    // Check sequence output shape: [batch_size, seq_len, hidden_size]
    assert_eq!(outputs.sequence_output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    
    // Check pooled output shape: [batch_size, hidden_size]
    if let Some(ref pooled) = outputs.pooled_output {
        assert_eq!(pooled.shape().dims(), &[batch_size, hidden_size]);
    }
    
    // Check MLM logits shape: [batch_size, seq_len, vocab_size]
    if let Some(ref mlm_logits) = outputs.mlm_logits {
        assert_eq!(mlm_logits.shape().dims(), &[batch_size, seq_len, vocab_size]);
    }
    
    // Check NSP logits shape: [batch_size, 2]
    if let Some(ref nsp_logits) = outputs.nsp_logits {
        assert_eq!(nsp_logits.shape().dims(), &[batch_size, 2]);
    }
    
    // Check that outputs are finite
    let seq_data = outputs.sequence_output.data();
    for &value in seq_data {
        assert!(value.is_finite(), "Sequence output should be finite");
    }
}

/// Test complete T5 model pipeline.
#[test]
fn test_t5_model_integration() {
    let config = T5Config::t5_base();
    let mut model = T5Model::<f32>::new(config).unwrap();
    model.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    // Test forward pass with encoder and decoder inputs
    let batch_size = 2;
    let encoder_seq_len = 12;
    let decoder_seq_len = 8;
    let vocab_size = 32128;
    let hidden_size = 768;
    
    // Create encoder input token IDs
    let mut encoder_input_data = Vec::with_capacity(batch_size * encoder_seq_len);
    for _ in 0..(batch_size * encoder_seq_len) {
        encoder_input_data.push((rand::random::<usize>() % vocab_size).min(vocab_size - 1));
    }
    let encoder_input_ids = CpuTensor::from_data(encoder_input_data, Shape::new(vec![batch_size, encoder_seq_len])).unwrap();
    
    // Create decoder input token IDs (starting with pad token 0)
    let mut decoder_input_data = Vec::with_capacity(batch_size * decoder_seq_len);
    for i in 0..(batch_size * decoder_seq_len) {
        if i % decoder_seq_len == 0 {
            decoder_input_data.push(0); // Start with pad token
        } else {
            decoder_input_data.push((rand::random::<usize>() % vocab_size).min(vocab_size - 1));
        }
    }
    let decoder_input_ids = CpuTensor::from_data(decoder_input_data, Shape::new(vec![batch_size, decoder_seq_len])).unwrap();
    
    // Create attention masks (all ones - no padding)
    let encoder_attention_mask_data = vec![1; batch_size * encoder_seq_len];
    let encoder_attention_mask = CpuTensor::from_data(encoder_attention_mask_data, Shape::new(vec![batch_size, encoder_seq_len])).unwrap();
    
    let decoder_attention_mask_data = vec![1; batch_size * decoder_seq_len];
    let decoder_attention_mask = CpuTensor::from_data(decoder_attention_mask_data, Shape::new(vec![batch_size, decoder_seq_len])).unwrap();
    
    // Forward pass
    let outputs = model.forward(
        &encoder_input_ids,
        &decoder_input_ids,
        Some(&encoder_attention_mask),
        Some(&decoder_attention_mask),
    ).unwrap();
    
    // Check encoder output shape: [batch_size, encoder_seq_len, hidden_size]
    assert_eq!(outputs.encoder_output.shape().dims(), &[batch_size, encoder_seq_len, hidden_size]);
    
    // Check decoder output shape: [batch_size, decoder_seq_len, hidden_size]
    assert_eq!(outputs.decoder_output.shape().dims(), &[batch_size, decoder_seq_len, hidden_size]);
    
    // Check logits shape: [batch_size, decoder_seq_len, vocab_size]
    assert_eq!(outputs.logits.shape().dims(), &[batch_size, decoder_seq_len, vocab_size]);
    
    // Check that outputs are finite
    let encoder_data = outputs.encoder_output.data();
    for &value in encoder_data {
        assert!(value.is_finite(), "Encoder output should be finite");
    }
    
    let decoder_data = outputs.decoder_output.data();
    for &value in decoder_data {
        assert!(value.is_finite(), "Decoder output should be finite");
    }
    
    let logits_data = outputs.logits.data();
    for &value in logits_data {
        assert!(value.is_finite(), "Logits should be finite");
    }
}

/// Test model parameter counting.
#[test]
fn test_model_parameter_counting() {
    // Test GPT parameter count estimation
    let gpt_config = GPTConfig::gpt2_small();
    let gpt_model = GPTModel::<f32>::new(gpt_config).unwrap();
    
    // GPT-2 Small should have approximately 117M parameters
    // This is a rough estimation test
    let estimated_params = gpt_model.config().base_config.vocab_size * gpt_model.config().base_config.hidden_size + // embeddings
                          gpt_model.config().base_config.num_layers * (
                              gpt_model.config().base_config.hidden_size * gpt_model.config().base_config.hidden_size * 4 + // attention
                              gpt_model.config().base_config.hidden_size * gpt_model.config().base_config.intermediate_size * 2 // FFN
                          );
    
    // Should be in the ballpark of 100M+ parameters
    assert!(estimated_params > 50_000_000, "GPT-2 Small should have 50M+ parameters");
    assert!(estimated_params < 200_000_000, "GPT-2 Small should have less than 200M parameters");
}

/// Test model training mode switching.
#[test]
fn test_training_mode_switching() {
    let config = GPTConfig::gpt2_small();
    let mut model = GPTModel::<f32>::new(config).unwrap();
    model.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    // Test switching to evaluation mode
    model.set_training(false);
    
    // Test switching back to training mode
    model.set_training(true);
    
    // Test forward pass in both modes
    let batch_size = 1;
    let seq_len = 5;
    let input_data = vec![1, 2, 3, 4, 5];
    let input_ids = CpuTensor::from_data(input_data, Shape::new(vec![batch_size, seq_len])).unwrap();
    
    // Training mode
    model.set_training(true);
    let train_output = model.forward(&input_ids, None).unwrap();
    
    // Evaluation mode
    model.set_training(false);
    let eval_output = model.forward(&input_ids, None).unwrap();
    
    // Outputs should have the same shape
    assert_eq!(train_output.shape(), eval_output.shape());
}

/// Test error handling for invalid inputs.
#[test]
fn test_error_handling() {
    let config = GPTConfig::gpt2_small();
    let model = GPTModel::<f32>::new(config).unwrap();
    
    // Test with wrong input shape (3D instead of 2D)
    let invalid_input = CpuTensorFactory::zeros(&Shape::new(vec![2, 10, 768])).unwrap();
    let result = model.forward(&invalid_input, None);
    assert!(result.is_err(), "Should fail with invalid input shape");
    
    // Test with empty input
    let empty_input = CpuTensor::from_data(vec![], Shape::new(vec![0, 0])).unwrap();
    let result = model.forward(&empty_input, None);
    assert!(result.is_err(), "Should fail with empty input");
}

/// Test model factory methods.
#[test]
fn test_model_factory_methods() {
    // Test GPT factory methods
    let gpt_small = GPTModel::<f32>::new(GPTConfig::gpt2_small()).unwrap();
    let gpt_medium = GPTModel::<f32>::new(GPTConfig::gpt2_medium()).unwrap();
    let gpt_large = GPTModel::<f32>::new(GPTConfig::gpt2_large()).unwrap();
    let gpt_xl = GPTModel::<f32>::new(GPTConfig::gpt2_xl()).unwrap();
    
    // Check that different sizes have different parameters
    assert_ne!(gpt_small.config().base_config.hidden_size, gpt_medium.config().base_config.hidden_size);
    assert_ne!(gpt_medium.config().base_config.hidden_size, gpt_large.config().base_config.hidden_size);
    assert_ne!(gpt_large.config().base_config.hidden_size, gpt_xl.config().base_config.hidden_size);
    
    // Test BERT factory methods
    let bert_base = BERTModel::<f32>::new(BERTConfig::bert_base()).unwrap();
    let bert_large = BERTModel::<f32>::new(BERTConfig::bert_large()).unwrap();
    
    assert_ne!(bert_base.config().base_config.hidden_size, bert_large.config().base_config.hidden_size);
    
    // Test T5 factory methods
    let t5_small = T5Model::<f32>::new(T5Config::t5_small()).unwrap();
    let t5_base = T5Model::<f32>::new(T5Config::t5_base()).unwrap();
    let t5_large = T5Model::<f32>::new(T5Config::t5_large()).unwrap();
    
    assert_ne!(t5_small.config().base_config.hidden_size, t5_base.config().base_config.hidden_size);
    assert_ne!(t5_base.config().base_config.hidden_size, t5_large.config().base_config.hidden_size);
}
