//! Integration tests for the model loading and management system.

use std::collections::HashMap;
use qilin_inference::model::*;

#[test]
fn test_model_config_basic_functionality() {
    // Test creating a basic model configuration
    let config = ModelConfig {
        architecture: "gpt".to_string(),
        model_type: "gpt2".to_string(),
        vocab_size: 50257,
        hidden_size: 768,
        num_layers: 12,
        num_attention_heads: 12,
        intermediate_size: Some(3072),
        max_position_embeddings: 1024,
        layer_norm_eps: 1e-5,
        dropout: 0.1,
        activation_function: "gelu".to_string(),
        use_cache: true,
        torch_dtype: "float32".to_string(),
        extra_params: HashMap::new(),
    };

    // Test basic properties
    assert_eq!(config.architecture, "gpt");
    assert_eq!(config.vocab_size, 50257);
    assert_eq!(config.hidden_size, 768);
    assert_eq!(config.num_layers, 12);
    assert_eq!(config.num_attention_heads, 12);

    // Test validation
    assert!(config.validate().is_ok());

    // Test serialization
    let json_str = serde_json::to_string(&config).unwrap();
    let deserialized: ModelConfig = serde_json::from_str(&json_str).unwrap();
    assert_eq!(config.architecture, deserialized.architecture);
    assert_eq!(config.vocab_size, deserialized.vocab_size);
}

#[test]
fn test_model_config_validation() {
    // Test invalid configuration
    let mut config = ModelConfig::default();
    config.vocab_size = 0; // Invalid vocab size
    
    let result = config.validate();
    assert!(result.is_err());
}

#[test]
fn test_loader_config_creation() {
    let config = LoaderConfig::default();
    
    assert_eq!(config.device_type, "cpu");
    assert!(config.use_mmap);
    assert!(config.lazy_loading);
}

#[test]
fn test_model_loader_creation() {
    let config = LoaderConfig::default();
    let _loader = ModelLoader::new(config);
    
    // Just test that creation succeeds without panicking
    assert!(true);
}

#[test]
fn test_model_registry_basic_functionality() {
    let registry = ModelRegistry::default();
    
    // Test that basic architectures are supported
    let supported = registry.list_supported_architectures();
    assert!(supported.contains(&"gpt".to_string()));
    assert!(supported.contains(&"bert".to_string()));
    assert!(supported.contains(&"transformer".to_string()));
    
    // Test factory info retrieval
    let factory_info = registry.get_factory_info("gpt");
    assert!(factory_info.is_some());
    
    let factory_info = registry.get_factory_info("nonexistent");
    assert!(factory_info.is_none());
}

#[test]
fn test_weight_mapper_basic_functionality() {
    let mut mapper = WeightMapper::new();
    
    // Test adding a mapping
    let mapping = WeightMapping {
        source_name: "pytorch.weight".to_string(),
        target_name: "qilin.weight".to_string(),
        transform: Some(WeightTransform::Transpose(vec![0, 1])),
    };
    
    mapper.add_mapping(mapping.clone());
    
    // Test that mapping was added (we can't access private fields, so just test no panic)
    assert!(true);
}

#[test]
fn test_weight_transform_types() {
    // Test that all transform types can be created
    let _transpose = WeightTransform::Transpose(vec![1, 0]);
    let _reshape = WeightTransform::Reshape(vec![768, 768]);
    let _scale = WeightTransform::Scale(0.5);
    let _split = WeightTransform::Split {
        dim: 0,
        chunks: 3
    };
    
    assert!(true);
}

#[test]
fn test_serialization_config_creation() {
    let config = SerializationConfig::default();
    
    assert_eq!(config.format, SerializationFormat::SafeTensors);
    assert_eq!(config.compression, CompressionType::None);
    assert!(config.save_config);
    assert!(config.save_metadata);
}

#[test]
fn test_model_serializer_creation() {
    let config = SerializationConfig::default();
    let _serializer = ModelSerializer::new(config);
    
    // Just test that creation succeeds
    assert!(true);
}

#[test]
fn test_checkpoint_metadata_creation() {
    let config = ModelConfig::default();
    let metadata = ModelMetadata {
        name: "test_model".to_string(),
        version: "1.0.0".to_string(),
        architecture: "gpt".to_string(),
        num_parameters: 1000,
        memory_usage: 4000,
        supported_dtypes: vec!["f32".to_string()],
        extra_info: HashMap::new(),
    };

    let checkpoint_metadata = CheckpointMetadata {
        version: "1.0.0".to_string(),
        created_at: chrono::Utc::now(),
        model_config: config,
        model_metadata: metadata,
        step: Some(1000),
        epoch: Some(10),
        loss: Some(0.5),
        extra_info: HashMap::new(),
    };

    assert_eq!(checkpoint_metadata.step, Some(1000));
    assert_eq!(checkpoint_metadata.epoch, Some(10));
    
    // Test serialization
    let json_str = serde_json::to_string(&checkpoint_metadata).unwrap();
    let deserialized: CheckpointMetadata = serde_json::from_str(&json_str).unwrap();
    assert_eq!(checkpoint_metadata.step, deserialized.step);
}

#[test]
fn test_model_metadata_serialization() {
    let metadata = ModelMetadata {
        name: "test_model".to_string(),
        version: "1.0.0".to_string(),
        architecture: "gpt".to_string(),
        num_parameters: 117000000,
        memory_usage: 468000000,
        supported_dtypes: vec!["f32".to_string(), "f16".to_string()],
        extra_info: HashMap::new(),
    };

    assert_eq!(metadata.name, "test_model");
    assert_eq!(metadata.num_parameters, 117000000);
    
    // Test serialization
    let json_str = serde_json::to_string(&metadata).unwrap();
    let deserialized: ModelMetadata = serde_json::from_str(&json_str).unwrap();
    assert_eq!(metadata.name, deserialized.name);
    assert_eq!(metadata.num_parameters, deserialized.num_parameters);
}

#[test]
fn test_complete_model_loading_workflow() {
    // Create a model configuration
    let config = ModelConfig {
        architecture: "gpt".to_string(),
        model_type: "gpt2".to_string(),
        vocab_size: 1000,
        hidden_size: 64,
        num_layers: 2,
        num_attention_heads: 4,
        intermediate_size: Some(256),
        max_position_embeddings: 512,
        layer_norm_eps: 1e-5,
        dropout: 0.1,
        activation_function: "gelu".to_string(),
        use_cache: true,
        torch_dtype: "float32".to_string(),
        extra_params: HashMap::new(),
    };

    // Validate the configuration
    assert!(config.validate().is_ok());

    // Create a model registry
    let registry = ModelRegistry::default();
    
    // Test that we can work with the architecture
    let supported = registry.list_supported_architectures();
    assert!(supported.contains(&config.architecture));
    
    // Test model factory info
    let factory_info = registry.get_factory_info("gpt");
    assert!(factory_info.is_some());
    
    // Create a loader
    let loader_config = LoaderConfig::default();
    let _loader = ModelLoader::new(loader_config);
    
    // Create a weight mapper
    let mut mapper = WeightMapper::new();
    let mapping = WeightMapping {
        source_name: "transformer.h.0.attn.c_attn.weight".to_string(),
        target_name: "layers.0.attention.query_key_value.weight".to_string(),
        transform: None,
    };
    mapper.add_mapping(mapping);
    
    // Create a serializer
    let serialization_config = SerializationConfig::default();
    let _serializer = ModelSerializer::new(serialization_config);
    
    // If we get here without panicking, the basic workflow is working
    assert!(true);
}
