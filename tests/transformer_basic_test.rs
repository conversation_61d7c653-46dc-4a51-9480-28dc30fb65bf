//! Basic integration test for Transformer implementation.

use qilin_inference::transformer::*;
use qilin_inference::transformer::variants::{GPTModel, GPTConfig, BERTModel, BERTConfig, T5Model, T5Config};
use qilin_inference::tensor::{<PERSON><PERSON><PERSON>, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::layers::ParameterInit;

#[test]
fn test_transformer_config_creation() {
    // Test basic configuration creation
    let config = TransformerConfig::new(1000, 512, 6, 8);
    
    assert_eq!(config.vocab_size, 1000);
    assert_eq!(config.hidden_size, 512);
    assert_eq!(config.num_layers, 6);
    assert_eq!(config.num_attention_heads, 8);
    assert_eq!(config.intermediate_size, 512 * 4); // Standard 4x expansion
    
    // Test configuration validation
    assert!(config.validate().is_ok());
}

#[test]
fn test_gpt_model_creation() {
    // Test GPT model creation
    let config = GPTConfig::gpt2_small();
    let model: GPTModel<f32> = GPTModel::new(config.clone()).unwrap();
    
    assert_eq!(model.config().base_config.model_type, ModelType::DecoderOnly);
    assert_eq!(model.config().base_config.vocab_size, 50257);
    assert_eq!(model.config().base_config.hidden_size, 768);
    assert_eq!(model.config().base_config.num_layers, 12);
}

#[test]
fn test_bert_model_creation() {
    // Test BERT model creation
    let config = BERTConfig::bert_base();
    let model: BERTModel<f32> = BERTModel::new(config.clone()).unwrap();
    
    assert_eq!(model.config().base_config.model_type, ModelType::EncoderOnly);
    assert_eq!(model.config().base_config.vocab_size, 30522);
    assert_eq!(model.config().base_config.hidden_size, 768);
    assert_eq!(model.config().base_config.num_layers, 12);
}

#[test]
fn test_t5_model_creation() {
    // Test T5 model creation
    let config = T5Config::t5_small();
    let model: T5Model<f32> = T5Model::new(config.clone()).unwrap();
    
    assert_eq!(model.config().base_config.model_type, ModelType::EncoderDecoder);
    assert_eq!(model.config().base_config.vocab_size, 32128);
    assert_eq!(model.config().base_config.hidden_size, 512);
    assert_eq!(model.config().base_config.num_encoder_layers, Some(6));
    assert_eq!(model.config().base_config.num_decoder_layers, Some(6));
}

#[test]
fn test_transformer_encoder_layer() {
    // Test encoder layer creation and basic functionality
    let config = EncoderLayerConfig {
        hidden_size: 512,
        num_attention_heads: 8,
        intermediate_size: 2048,
        dropout: 0.1,
        attention_dropout: 0.1,
        activation_function: ActivationType::ReLU,
        normalization_type: NormalizationType::LayerNorm,
        pre_norm: false,
        layer_norm_eps: 1e-5,
        attention_bias: true,
        use_bias: true,
        use_gated_ffn: false,
    };
    
    let mut layer: TransformerEncoderLayer<f32> = TransformerEncoderLayer::new(config).unwrap();
    
    // Test parameter initialization
    assert!(layer.init_parameters(ParameterInit::XavierUniform).is_ok());
}

#[test]
fn test_transformer_decoder_layer() {
    // Test decoder layer creation and basic functionality
    let config = DecoderLayerConfig {
        hidden_size: 512,
        num_attention_heads: 8,
        intermediate_size: 2048,
        dropout: 0.1,
        attention_dropout: 0.1,
        cross_attention_dropout: Some(0.1),
        activation_function: ActivationType::ReLU,
        normalization_type: NormalizationType::LayerNorm,
        pre_norm: false,
        layer_norm_eps: 1e-5,
        attention_bias: true,
        use_bias: true,
        use_gated_ffn: false,
        decoder_only: false,
    };
    
    let mut layer: TransformerDecoderLayer<f32> = TransformerDecoderLayer::new(config).unwrap();
    
    // Test parameter initialization
    assert!(layer.init_parameters(ParameterInit::XavierUniform).is_ok());
}

#[test]
fn test_transformer_block() {
    // Test transformer block creation
    let config = TransformerConfig::new(1000, 512, 6, 8);
    let mut block: TransformerBlock<f32> = TransformerBlock::new(config).unwrap();
    
    // Test parameter initialization
    assert!(block.init_parameters(ParameterInit::XavierUniform).is_ok());
}

#[test]
fn test_position_encoding() {
    // Test learned position encoding
    let config = PositionEncodingConfig::new(PositionEncodingType::Learned, 1024, 512);
    let mut learned_pos = LearnedPositionEmbedding::<f32>::new(config.clone()).unwrap();
    assert!(learned_pos.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 }).is_ok());
    assert_eq!(learned_pos.max_length(), 1024);
    assert_eq!(learned_pos.d_model(), 512);

    // Test sinusoidal position encoding
    let config = PositionEncodingConfig::new(PositionEncodingType::Sinusoidal, 1024, 512);
    let sinusoidal_pos = SinusoidalPositionEncoding::<f32>::new(config).unwrap();
    assert_eq!(sinusoidal_pos.max_length(), 1024);
    assert_eq!(sinusoidal_pos.d_model(), 512);

    // Test RoPE
    let config = PositionEncodingConfig::new(PositionEncodingType::RoPE, 1024, 512);
    let rope = RotaryPositionEmbedding::<f32>::new(config).unwrap();
    assert_eq!(rope.max_length(), 1024);
    assert_eq!(rope.d_model(), 512);
}

#[test]
fn test_preset_configurations() {
    // Test GPT presets
    let gpt2_small = GPTConfig::gpt2_small();
    assert_eq!(gpt2_small.base_config.hidden_size, 768);
    assert_eq!(gpt2_small.base_config.num_layers, 12);

    let gpt2_medium = GPTConfig::gpt2_medium();
    assert_eq!(gpt2_medium.base_config.hidden_size, 1024);
    assert_eq!(gpt2_medium.base_config.num_layers, 24);

    // Test BERT presets
    let bert_base = BERTConfig::bert_base();
    assert_eq!(bert_base.base_config.hidden_size, 768);
    assert_eq!(bert_base.base_config.num_layers, 12);

    let bert_large = BERTConfig::bert_large();
    assert_eq!(bert_large.base_config.hidden_size, 1024);
    assert_eq!(bert_large.base_config.num_layers, 24);

    // Test T5 presets
    let t5_small = T5Config::t5_small();
    assert_eq!(t5_small.base_config.hidden_size, 512);
    assert_eq!(t5_small.base_config.num_encoder_layers, Some(6));
    assert_eq!(t5_small.base_config.num_decoder_layers, Some(6));

    let t5_base = T5Config::t5_base();
    assert_eq!(t5_base.base_config.hidden_size, 768);
    assert_eq!(t5_base.base_config.num_encoder_layers, Some(12));
    assert_eq!(t5_base.base_config.num_decoder_layers, Some(12));
}
