//! Model serialization and checkpoint management.
//!
//! This module provides functionality for saving and loading model weights,
//! configurations, and training checkpoints.

use std::collections::HashMap;
use std::fs::File;
use std::io::{BufWriter, Write};
use std::path::{Path, PathBuf};
use flate2::Compression;
use flate2::write::GzEncoder;
use serde::{Deserialize, Serialize};

use crate::error::{ModelError, ErrorContext};
use crate::tensor::{Tensor, Numeric, DType, cpu::CpuTensor};
use super::config::ModelConfig;
use super::registry::{Model, ModelMetadata};

/// Checkpoint metadata.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckpointMetadata {
    /// Checkpoint version.
    pub version: String,
    /// Creation timestamp.
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// Model configuration.
    pub model_config: ModelConfig,
    /// Model metadata.
    pub model_metadata: ModelMetadata,
    /// Training step (if applicable).
    pub step: Option<usize>,
    /// Training epoch (if applicable).
    pub epoch: Option<usize>,
    /// Loss value (if applicable).
    pub loss: Option<f32>,
    /// Additional metadata.
    pub extra_info: HashMap<String, serde_json::Value>,
}

/// Serialization format options.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SerializationFormat {
    /// SafeTensors format (recommended).
    SafeTensors,
    /// PyTorch format.
    PyTorch,
    /// Custom binary format.
    Binary,
    /// JSON format (for small models/debugging).
    Json,
}

/// Compression options.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CompressionType {
    /// No compression.
    None,
    /// Gzip compression.
    Gzip,
    /// Zstd compression.
    Zstd,
}

/// Serialization configuration.
#[derive(Debug, Clone)]
pub struct SerializationConfig {
    /// Output format.
    pub format: SerializationFormat,
    /// Compression type.
    pub compression: CompressionType,
    /// Compression level (0-9 for gzip, 1-22 for zstd).
    pub compression_level: u32,
    /// Whether to save model configuration.
    pub save_config: bool,
    /// Whether to save metadata.
    pub save_metadata: bool,
    /// Whether to create a backup before overwriting.
    pub create_backup: bool,
}

impl Default for SerializationConfig {
    fn default() -> Self {
        Self {
            format: SerializationFormat::SafeTensors,
            compression: CompressionType::None,
            compression_level: 6,
            save_config: true,
            save_metadata: true,
            create_backup: true,
        }
    }
}

/// Model serializer for saving and loading models.
pub struct ModelSerializer {
    config: SerializationConfig,
}

impl ModelSerializer {
    /// Create a new model serializer.
    pub fn new(config: SerializationConfig) -> Self {
        Self { config }
    }

    /// Create a serializer with default configuration.
    pub fn default() -> Self {
        Self::new(SerializationConfig::default())
    }

    /// Save a model to disk.
    pub fn save_model<T: Numeric>(
        &self,
        model: &dyn Model<T>,
        weights: &HashMap<String, CpuTensor<T>>,
        output_path: &Path,
    ) -> Result<(), ModelError> {
        // Create output directory if it doesn't exist
        if let Some(parent) = output_path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| {
                ModelError::WeightLoadingFailed {
                    path: parent.to_path_buf(),
                    io_error: Some(e),
                    context: Some(ErrorContext::new("create_output_dir", "model::serialization")),
                }
            })?;
        }

        // Create backup if requested
        if self.config.create_backup && output_path.exists() {
            self.create_backup(output_path)?;
        }

        // Save weights
        self.save_weights(weights, output_path)?;

        // Save configuration if requested
        if self.config.save_config {
            let config_path = self.get_config_path(output_path);
            model.config().save_json(&config_path)?;
        }

        // Save metadata if requested
        if self.config.save_metadata {
            let metadata_path = self.get_metadata_path(output_path);
            self.save_metadata(&model.metadata(), &metadata_path)?;
        }

        Ok(())
    }

    /// Save model weights.
    fn save_weights<T: Numeric>(
        &self,
        weights: &HashMap<String, CpuTensor<T>>,
        output_path: &Path,
    ) -> Result<(), ModelError> {
        match self.config.format {
            SerializationFormat::SafeTensors => {
                self.save_safetensors(weights, output_path)
            }
            SerializationFormat::PyTorch => {
                self.save_pytorch(weights, output_path)
            }
            SerializationFormat::Binary => {
                self.save_binary(weights, output_path)
            }
            SerializationFormat::Json => {
                self.save_json(weights, output_path)
            }
        }
    }

    /// Save weights in SafeTensors format.
    fn save_safetensors<T: Numeric>(
        &self,
        weights: &HashMap<String, CpuTensor<T>>,
        output_path: &Path,
    ) -> Result<(), ModelError> {
        // This is a simplified implementation
        // A full implementation would use the safetensors crate
        Err(ModelError::UnsupportedArchitecture {
            architecture: "safetensors_save_not_implemented".to_string(),
            supported_architectures: vec![],
            context: Some(ErrorContext::new("save_safetensors", "model::serialization")),
        })
    }

    /// Save weights in PyTorch format.
    fn save_pytorch<T: Numeric>(
        &self,
        weights: &HashMap<String, CpuTensor<T>>,
        output_path: &Path,
    ) -> Result<(), ModelError> {
        // This is a simplified implementation
        // A full implementation would use PyTorch serialization
        Err(ModelError::UnsupportedArchitecture {
            architecture: "pytorch_save_not_implemented".to_string(),
            supported_architectures: vec![],
            context: Some(ErrorContext::new("save_pytorch", "model::serialization")),
        })
    }

    /// Save weights in binary format.
    fn save_binary<T: Numeric>(
        &self,
        weights: &HashMap<String, CpuTensor<T>>,
        output_path: &Path,
    ) -> Result<(), ModelError> {
        let file = File::create(output_path).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: output_path.to_path_buf(),
                io_error: Some(e),
                context: Some(ErrorContext::new("create_binary_file", "model::serialization")),
            }
        })?;

        let writer: Box<dyn Write> = match self.config.compression {
            CompressionType::None => Box::new(BufWriter::new(file)),
            CompressionType::Gzip => {
                let encoder = GzEncoder::new(file, Compression::new(self.config.compression_level));
                Box::new(BufWriter::new(encoder))
            }
            CompressionType::Zstd => {
                // Would use zstd encoder here
                return Err(ModelError::UnsupportedArchitecture {
                    architecture: "zstd_not_implemented".to_string(),
                    supported_architectures: vec![],
                    context: Some(ErrorContext::new("save_binary", "model::serialization")),
                });
            }
        };

        // Serialize weights to binary format
        // This is a simplified implementation
        drop(writer);
        
        Ok(())
    }

    /// Save weights in JSON format.
    fn save_json<T: Numeric>(
        &self,
        weights: &HashMap<String, CpuTensor<T>>,
        output_path: &Path,
    ) -> Result<(), ModelError> {
        // Convert tensors to serializable format
        let mut serializable_weights = HashMap::new();
        
        for (name, tensor) in weights {
            let tensor_data = SerializableTensor {
                shape: tensor.shape().dims().to_vec(),
                dtype: format!("{:?}", T::dtype()),
                data: self.tensor_to_vec(tensor)?,
            };
            serializable_weights.insert(name.clone(), tensor_data);
        }

        let content = serde_json::to_string_pretty(&serializable_weights).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to serialize weights: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("serialize_json", "model::serialization")),
            }
        })?;

        std::fs::write(output_path, content).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: output_path.to_path_buf(),
                io_error: Some(e),
                context: Some(ErrorContext::new("write_json", "model::serialization")),
            }
        })
    }

    /// Convert tensor to vector for serialization.
    fn tensor_to_vec<T: Numeric>(&self, tensor: &CpuTensor<T>) -> Result<Vec<f32>, ModelError> {
        // This is a simplified implementation
        // In practice, you'd need to access the tensor's raw data
        let size = tensor.size();
        Ok(vec![0.0; size]) // Placeholder
    }

    /// Save metadata to file.
    fn save_metadata(
        &self,
        metadata: &ModelMetadata,
        path: &Path,
    ) -> Result<(), ModelError> {
        let content = serde_json::to_string_pretty(metadata).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to serialize metadata: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("serialize_metadata", "model::serialization")),
            }
        })?;

        std::fs::write(path, content).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: path.to_path_buf(),
                io_error: Some(e),
                context: Some(ErrorContext::new("write_metadata", "model::serialization")),
            }
        })
    }

    /// Create a backup of an existing file.
    fn create_backup(&self, path: &Path) -> Result<(), ModelError> {
        let backup_path = path.with_extension(
            format!("{}.backup", path.extension().and_then(|s| s.to_str()).unwrap_or(""))
        );

        std::fs::copy(path, &backup_path).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: backup_path,
                io_error: Some(e),
                context: Some(ErrorContext::new("create_backup", "model::serialization")),
            }
        })?;

        Ok(())
    }

    /// Get configuration file path.
    fn get_config_path(&self, model_path: &Path) -> PathBuf {
        model_path.with_extension("config.json")
    }

    /// Get metadata file path.
    fn get_metadata_path(&self, model_path: &Path) -> PathBuf {
        model_path.with_extension("metadata.json")
    }

    /// Save a training checkpoint.
    pub fn save_checkpoint<T: Numeric>(
        &self,
        model: &dyn Model<T>,
        weights: &HashMap<String, CpuTensor<T>>,
        checkpoint_path: &Path,
        step: Option<usize>,
        epoch: Option<usize>,
        loss: Option<f32>,
    ) -> Result<(), ModelError> {
        let metadata = CheckpointMetadata {
            version: "1.0.0".to_string(),
            created_at: chrono::Utc::now(),
            model_config: model.config().clone(),
            model_metadata: model.metadata(),
            step,
            epoch,
            loss,
            extra_info: HashMap::new(),
        };

        // Save checkpoint metadata
        let metadata_path = checkpoint_path.with_extension("checkpoint.json");
        let metadata_content = serde_json::to_string_pretty(&metadata).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to serialize checkpoint metadata: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("serialize_checkpoint", "model::serialization")),
            }
        })?;

        std::fs::write(&metadata_path, metadata_content).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: metadata_path,
                io_error: Some(e),
                context: Some(ErrorContext::new("write_checkpoint", "model::serialization")),
            }
        })?;

        // Save model weights
        self.save_model(model, weights, checkpoint_path)
    }

    /// Load a training checkpoint.
    pub fn load_checkpoint(
        &self,
        checkpoint_path: &Path,
    ) -> Result<CheckpointMetadata, ModelError> {
        let metadata_path = checkpoint_path.with_extension("checkpoint.json");
        
        let content = std::fs::read_to_string(&metadata_path).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: metadata_path.clone(),
                io_error: Some(e),
                context: Some(ErrorContext::new("read_checkpoint", "model::serialization")),
            }
        })?;

        let metadata: CheckpointMetadata = serde_json::from_str(&content).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to parse checkpoint metadata: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("parse_checkpoint", "model::serialization")),
            }
        })?;

        Ok(metadata)
    }
}

/// Serializable tensor representation.
#[derive(Debug, Clone, Serialize, Deserialize)]
struct SerializableTensor {
    shape: Vec<usize>,
    dtype: String,
    data: Vec<f32>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_serializer_creation() {
        let serializer = ModelSerializer::default();
        assert_eq!(serializer.config.format, SerializationFormat::SafeTensors);
    }

    #[test]
    fn test_config_paths() {
        let serializer = ModelSerializer::default();
        let model_path = Path::new("model.safetensors");
        
        let config_path = serializer.get_config_path(model_path);
        assert_eq!(config_path, Path::new("model.config.json"));
        
        let metadata_path = serializer.get_metadata_path(model_path);
        assert_eq!(metadata_path, Path::new("model.metadata.json"));
    }

    #[test]
    fn test_checkpoint_metadata() {
        let config = ModelConfig::default();
        let metadata = ModelMetadata {
            name: "test_model".to_string(),
            version: "1.0.0".to_string(),
            architecture: "gpt".to_string(),
            num_parameters: 1000,
            memory_usage: 4000,
            supported_dtypes: vec!["f32".to_string()],
            extra_info: HashMap::new(),
        };

        let checkpoint_metadata = CheckpointMetadata {
            version: "1.0.0".to_string(),
            created_at: chrono::Utc::now(),
            model_config: config,
            model_metadata: metadata,
            step: Some(1000),
            epoch: Some(10),
            loss: Some(0.5),
            extra_info: HashMap::new(),
        };

        // Test serialization
        let serialized = serde_json::to_string(&checkpoint_metadata).unwrap();
        let deserialized: CheckpointMetadata = serde_json::from_str(&serialized).unwrap();
        assert_eq!(deserialized.step, Some(1000));
    }
}
