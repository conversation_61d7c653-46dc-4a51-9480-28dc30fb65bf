//! Tests for the model loading and management system.

use std::collections::HashMap;
use std::path::PathBuf;

use crate::error::ModelError;
use super::*;

#[cfg(test)]
mod config_tests {
    use super::*;

    #[test]
    fn test_model_config_creation() {
        let config = ModelConfig {
            architecture: "gpt".to_string(),
            model_type: "gpt2".to_string(),
            vocab_size: 50257,
            hidden_size: 768,
            num_layers: 12,
            num_attention_heads: 12,
            intermediate_size: Some(3072),
            max_position_embeddings: 1024,
            layer_norm_eps: 1e-5,
            dropout: 0.1,
            activation_function: "gelu".to_string(),
            use_cache: true,
            torch_dtype: "float32".to_string(),
            extra_params: HashMap::new(),
        };

        assert_eq!(config.architecture, "gpt");
        assert_eq!(config.vocab_size, 50257);
        assert_eq!(config.hidden_size, 768);
    }

    #[test]
    fn test_model_config_validation() {
        let mut config = ModelConfig::default();
        config.vocab_size = 0; // Invalid vocab size
        
        let result = config.validate();
        assert!(result.is_err());
        
        if let Err(ModelError::InvalidConfiguration { message, .. }) = result {
            assert!(message.contains("vocab_size"));
        } else {
            panic!("Expected InvalidConfiguration error");
        }
    }

    #[test]
    fn test_model_config_json_serialization() {
        let config = ModelConfig::default();
        
        let json_str = serde_json::to_string(&config).unwrap();
        let deserialized: ModelConfig = serde_json::from_str(&json_str).unwrap();
        
        assert_eq!(config.architecture, deserialized.architecture);
        assert_eq!(config.vocab_size, deserialized.vocab_size);
    }
}

#[cfg(test)]
mod loader_tests {
    use super::*;

    #[test]
    fn test_loader_config_creation() {
        let config = LoaderConfig::default();
        
        assert_eq!(config.device_type, "cpu");
        assert_eq!(config.dtype, DType::F32);
        assert!(config.use_mmap);
        assert!(config.lazy_loading);
    }

    #[test]
    fn test_model_loader_creation() {
        let config = LoaderConfig::default();
        let _loader = ModelLoader::new(config);

        // Just test that creation succeeds
        assert!(true);
    }
}

#[cfg(test)]
mod registry_tests {
    use super::*;

    #[test]
    fn test_model_registry_creation() {
        let registry = ModelRegistry::new();
        
        assert!(registry.list_supported_architectures().contains(&"gpt".to_string()));
        assert!(registry.list_supported_architectures().contains(&"transformer".to_string()));
    }

    #[test]
    fn test_architecture_support() {
        let registry = ModelRegistry::new();

        // Test that supported architectures are listed
        let supported = registry.list_supported_architectures();
        assert!(supported.contains(&"gpt".to_string()));
        assert!(supported.contains(&"transformer".to_string()));
    }

    #[test]
    fn test_model_metadata() {
        let metadata = ModelMetadata {
            name: "test_model".to_string(),
            version: "1.0.0".to_string(),
            architecture: "gpt".to_string(),
            num_parameters: 117000000,
            memory_usage: 468000000,
            supported_dtypes: vec!["f32".to_string(), "f16".to_string()],
            extra_info: HashMap::new(),
        };

        assert_eq!(metadata.name, "test_model");
        assert_eq!(metadata.num_parameters, 117000000);
        
        // Test serialization
        let json_str = serde_json::to_string(&metadata).unwrap();
        let deserialized: ModelMetadata = serde_json::from_str(&json_str).unwrap();
        assert_eq!(metadata.name, deserialized.name);
    }
}

#[cfg(test)]
mod mapping_tests {
    use super::*;

    #[test]
    fn test_weight_mapper_creation() {
        let _mapper = WeightMapper::new();

        // Just test that creation succeeds
        assert!(true);
    }

    #[test]
    fn test_weight_mapping_addition() {
        let mut mapper = WeightMapper::new();

        let mapping = WeightMapping {
            source_name: "pytorch.weight".to_string(),
            target_name: "qilin.weight".to_string(),
            transform: Some(WeightTransform::Transpose(vec![0, 1])),
        };

        mapper.add_mapping(mapping.clone());
        // Just test that addition succeeds
        assert!(true);
    }

    #[test]
    fn test_weight_transform_creation() {
        let transpose = WeightTransform::Transpose(vec![1, 0]);
        let reshape = WeightTransform::Reshape(vec![768, 768]);
        let scale = WeightTransform::Scale(0.5);
        
        match transpose {
            WeightTransform::Transpose(dims) => assert_eq!(dims, vec![1, 0]),
            _ => panic!("Expected Transpose transform"),
        }
        
        match reshape {
            WeightTransform::Reshape(shape) => assert_eq!(shape, vec![768, 768]),
            _ => panic!("Expected Reshape transform"),
        }
        
        match scale {
            WeightTransform::Scale(factor) => assert_eq!(factor, 0.5),
            _ => panic!("Expected Scale transform"),
        }
    }
}

#[cfg(test)]
mod serialization_tests {
    use super::*;

    #[test]
    fn test_serialization_config_creation() {
        let config = SerializationConfig::default();
        
        assert_eq!(config.format, SerializationFormat::SafeTensors);
        assert_eq!(config.compression, CompressionType::None);
        assert!(config.save_config);
        assert!(config.save_metadata);
    }

    #[test]
    fn test_model_serializer_creation() {
        let config = SerializationConfig::default();
        let _serializer = ModelSerializer::new(config);

        // Just test that creation succeeds
        assert!(true);
    }

    #[test]
    fn test_checkpoint_metadata_creation() {
        let config = ModelConfig::default();
        let metadata = ModelMetadata {
            name: "test_model".to_string(),
            version: "1.0.0".to_string(),
            architecture: "gpt".to_string(),
            num_parameters: 1000,
            memory_usage: 4000,
            supported_dtypes: vec!["f32".to_string()],
            extra_info: HashMap::new(),
        };

        let checkpoint_metadata = CheckpointMetadata {
            version: "1.0.0".to_string(),
            created_at: chrono::Utc::now(),
            model_config: config,
            model_metadata: metadata,
            step: Some(1000),
            epoch: Some(10),
            loss: Some(0.5),
            extra_info: HashMap::new(),
        };

        assert_eq!(checkpoint_metadata.step, Some(1000));
        assert_eq!(checkpoint_metadata.epoch, Some(10));
        
        // Test serialization
        let json_str = serde_json::to_string(&checkpoint_metadata).unwrap();
        let deserialized: CheckpointMetadata = serde_json::from_str(&json_str).unwrap();
        assert_eq!(checkpoint_metadata.step, deserialized.step);
    }

    #[test]
    fn test_serialization_formats() {
        // Test that serialization formats can be created
        let _safetensors = SerializationFormat::SafeTensors;
        let _pytorch = SerializationFormat::PyTorch;
        let _binary = SerializationFormat::Binary;
        let _json = SerializationFormat::Json;

        assert!(true);
    }
}

#[cfg(test)]
mod integration_tests {
    use super::*;

    #[test]
    fn test_model_loading_workflow() {
        // Create a simple model configuration
        let config = ModelConfig {
            architecture: "gpt".to_string(),
            model_type: "gpt2".to_string(),
            vocab_size: 1000,
            hidden_size: 64,
            num_layers: 2,
            num_attention_heads: 4,
            intermediate_size: Some(256),
            max_position_embeddings: 512,
            layer_norm_eps: 1e-5,
            dropout: 0.1,
            activation_function: "gelu".to_string(),
            use_cache: true,
            torch_dtype: "float32".to_string(),
            extra_params: HashMap::new(),
        };

        // Validate the configuration
        assert!(config.validate().is_ok());

        // Create a model registry
        let registry = ModelRegistry::new();
        
        // Test that the architecture is supported
        let supported = registry.list_supported_architectures();
        assert!(supported.contains(&config.architecture));
        
        // Test model creation (this would normally require actual weights)
        // For now, just test that the factory exists
        let factory_info = registry.get_factory_info("gpt");
        assert!(factory_info.is_some());
    }
}
