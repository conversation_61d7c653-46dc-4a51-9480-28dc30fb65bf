//! Model weight loading system.
//!
//! This module provides functionality for loading model weights from various formats:
//! - SafeTensors format (recommended)
//! - PyTorch format (.pth, .bin)
//! - GGUF format (quantized models)
//! - HuggingFace compatible formats

use std::collections::HashMap;
use std::fs::File;
use std::io::{BufReader, Read};
use std::path::{Path, PathBuf};
use memmap2::{Mmap, MmapOptions};
use serde::{Deserialize, Serialize};

use crate::error::{ModelError, ErrorContext};
use crate::tensor::{Tensor, Numeric, DType, Shape, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use super::config::ModelConfig;

/// SafeTensors header structure.
#[derive(Debug, Deserialize)]
struct SafeTensorsHeader {
    /// Tensor metadata.
    #[serde(flatten)]
    tensors: HashMap<String, TensorInfo>,
}

/// Tensor information in SafeTensors format.
#[derive(Debug, Deserialize)]
struct TensorInfo {
    /// Data type of the tensor.
    dtype: String,
    /// Shape of the tensor.
    shape: Vec<usize>,
    /// Data offsets [start, end].
    data_offsets: [usize; 2],
}

/// PyTorch weight file header.
#[derive(Debug, Deserialize)]
struct PyTorchHeader {
    /// Model state dictionary.
    state_dict: HashMap<String, PyTorchTensorInfo>,
}

/// PyTorch tensor information.
#[derive(Debug, Deserialize)]
struct PyTorchTensorInfo {
    /// Data type.
    dtype: String,
    /// Shape.
    shape: Vec<usize>,
    /// Storage offset.
    storage_offset: usize,
    /// Storage size.
    storage_size: usize,
}

/// Model loader configuration.
#[derive(Debug, Clone)]
pub struct LoaderConfig {
    /// Cache directory for downloaded models.
    pub cache_dir: PathBuf,
    /// Device type to load tensors on.
    pub device_type: String,
    /// Default data type for loaded tensors.
    pub dtype: DType,
    /// Enable memory mapping for large files.
    pub use_mmap: bool,
    /// Maximum memory usage for caching (in bytes).
    pub max_cache_size: usize,
    /// Enable lazy loading of weights.
    pub lazy_loading: bool,
}

impl Default for LoaderConfig {
    fn default() -> Self {
        Self {
            cache_dir: PathBuf::from(".cache/models"),
            device_type: "cpu".to_string(),
            dtype: DType::F32,
            use_mmap: true,
            max_cache_size: 8 * 1024 * 1024 * 1024, // 8GB
            lazy_loading: true,
        }
    }
}

/// Model weight loader.
pub struct ModelLoader {
    config: LoaderConfig,
    /// Cache for loaded weights.
    weight_cache: parking_lot::RwLock<HashMap<PathBuf, HashMap<String, Box<dyn std::any::Any + Send + Sync>>>>,
    /// Memory mapped files cache.
    mmap_cache: parking_lot::RwLock<HashMap<PathBuf, Mmap>>,
}

impl ModelLoader {
    /// Create a new model loader with the given configuration.
    pub fn new(config: LoaderConfig) -> Result<Self, ModelError> {
        // Ensure cache directory exists
        if !config.cache_dir.exists() {
            std::fs::create_dir_all(&config.cache_dir).map_err(|e| {
                ModelError::WeightLoadingFailed {
                    path: config.cache_dir.clone(),
                    io_error: Some(e),
                    context: Some(ErrorContext::new("create_cache_dir", "model::loader")),
                }
            })?;
        }

        Ok(Self {
            config,
            weight_cache: parking_lot::RwLock::new(HashMap::new()),
            mmap_cache: parking_lot::RwLock::new(HashMap::new()),
        })
    }

    /// Create a model loader with default configuration.
    pub fn default() -> Result<Self, ModelError> {
        Self::new(LoaderConfig::default())
    }

    /// Load model weights from a file.
    pub fn load_weights<T: Numeric>(
        &self,
        weights_path: &Path,
    ) -> Result<HashMap<String, CpuTensor<T>>, ModelError> {
        // Check cache first
        if let Some(cached_weights) = self.get_cached_weights::<T>(weights_path) {
            return Ok(cached_weights);
        }

        // Determine file format and load accordingly
        let weights = match weights_path.extension().and_then(|ext| ext.to_str()) {
            Some("safetensors") => self.load_safetensors(weights_path)?,
            Some("pth") | Some("bin") => self.load_pytorch_weights(weights_path)?,
            Some("gguf") => self.load_gguf_weights(weights_path)?,
            _ => {
                return Err(ModelError::WeightLoadingFailed {
                    path: weights_path.to_path_buf(),
                    io_error: None,
                    context: Some(ErrorContext::new("unsupported_format", "model::loader")),
                });
            }
        };

        // Cache the loaded weights
        self.cache_weights(weights_path, &weights);

        Ok(weights)
    }

    /// Load SafeTensors format weights.
    pub fn load_safetensors<T: Numeric>(
        &self,
        file_path: &Path,
    ) -> Result<HashMap<String, CpuTensor<T>>, ModelError> {
        let file = File::open(file_path).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: file_path.to_path_buf(),
                io_error: Some(e),
                context: Some(ErrorContext::new("open_file", "model::loader")),
            }
        })?;

        let mmap = unsafe {
            MmapOptions::new().map(&file).map_err(|e| {
                ModelError::WeightLoadingFailed {
                    path: file_path.to_path_buf(),
                    io_error: Some(e),
                    context: Some(ErrorContext::new("mmap_file", "model::loader")),
                }
            })?
        };

        // Parse SafeTensors header
        let header_size = u64::from_le_bytes(
            mmap[0..8].try_into().map_err(|_| {
                ModelError::WeightLoadingFailed {
                    path: file_path.to_path_buf(),
                    io_error: None,
                    context: Some(ErrorContext::new("invalid_header", "model::loader")),
                }
            })?
        ) as usize;

        let header_bytes = &mmap[8..8 + header_size];
        let header: SafeTensorsHeader = serde_json::from_slice(header_bytes).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: file_path.to_path_buf(),
                io_error: None,
                context: Some(ErrorContext::new("parse_header", "model::loader")),
            }
        })?;

        let mut tensors = HashMap::new();

        for (name, info) in header.tensors {
            let start = 8 + header_size + info.data_offsets[0];
            let end = 8 + header_size + info.data_offsets[1];
            let data = &mmap[start..end];

            let tensor = self.create_tensor_from_bytes::<T>(data, &info.shape, &info.dtype)?;
            tensors.insert(name, tensor);
        }

        // Cache the memory map
        self.mmap_cache.write().insert(file_path.to_path_buf(), mmap);

        Ok(tensors)
    }

    /// Load PyTorch format weights (.pth, .bin).
    pub fn load_pytorch_weights<T: Numeric>(
        &self,
        file_path: &Path,
    ) -> Result<HashMap<String, CpuTensor<T>>, ModelError> {
        // For now, return an error as PyTorch loading requires more complex implementation
        Err(ModelError::WeightLoadingFailed {
            path: file_path.to_path_buf(),
            io_error: None,
            context: Some(ErrorContext::new("pytorch_not_implemented", "model::loader")),
        })
    }

    /// Load GGUF format weights.
    pub fn load_gguf_weights<T: Numeric>(
        &self,
        file_path: &Path,
    ) -> Result<HashMap<String, CpuTensor<T>>, ModelError> {
        // For now, return an error as GGUF loading requires more complex implementation
        Err(ModelError::WeightLoadingFailed {
            path: file_path.to_path_buf(),
            io_error: None,
            context: Some(ErrorContext::new("gguf_not_implemented", "model::loader")),
        })
    }

    /// Create a tensor from raw bytes.
    fn create_tensor_from_bytes<T: Numeric>(
        &self,
        data: &[u8],
        shape: &[usize],
        dtype: &str,
    ) -> Result<CpuTensor<T>, ModelError> {
        let shape = Shape::new(shape.to_vec());
        
        // Convert data based on dtype
        match dtype {
            "F32" | "float32" => {
                if T::dtype() != DType::F32 {
                    return Err(ModelError::WeightLoadingFailed {
                        path: PathBuf::new(),
                        io_error: None,
                        context: Some(ErrorContext::new("dtype_mismatch", "model::loader")),
                    });
                }
                
                let float_data: &[f32] = unsafe {
                    std::slice::from_raw_parts(
                        data.as_ptr() as *const f32,
                        data.len() / std::mem::size_of::<f32>(),
                    )
                };
                
                let converted_data: Vec<T> = float_data.iter()
                    .map(|&x| T::from_f32(x))
                    .collect();
                
                CpuTensorFactory::from_vec(converted_data, &shape).map_err(|e| {
                    ModelError::Tensor(e)
                })
            }
            "F64" | "float64" => {
                if T::dtype() != DType::F64 {
                    return Err(ModelError::WeightLoadingFailed {
                        path: PathBuf::new(),
                        io_error: None,
                        context: Some(ErrorContext::new("dtype_mismatch", "model::loader")),
                    });
                }
                
                let float_data: &[f64] = unsafe {
                    std::slice::from_raw_parts(
                        data.as_ptr() as *const f64,
                        data.len() / std::mem::size_of::<f64>(),
                    )
                };
                
                let converted_data: Vec<T> = float_data.iter()
                    .map(|&x| T::from_f32(x as f32))
                    .collect();
                
                CpuTensorFactory::from_vec(converted_data, &shape).map_err(|e| {
                    ModelError::Tensor(e)
                })
            }
            _ => Err(ModelError::WeightLoadingFailed {
                path: PathBuf::new(),
                io_error: None,
                context: Some(ErrorContext::new("unsupported_dtype", "model::loader")),
            })
        }
    }

    /// Get cached weights if available.
    fn get_cached_weights<T: Numeric>(
        &self,
        path: &Path,
    ) -> Option<HashMap<String, CpuTensor<T>>> {
        // For now, return None as caching implementation is complex
        None
    }

    /// Cache loaded weights.
    fn cache_weights<T: Numeric>(
        &self,
        path: &Path,
        weights: &HashMap<String, CpuTensor<T>>,
    ) {
        // For now, do nothing as caching implementation is complex
    }

    /// Clear all caches.
    pub fn clear_cache(&self) {
        self.weight_cache.write().clear();
        self.mmap_cache.write().clear();
    }

    /// Get cache statistics.
    pub fn cache_stats(&self) -> (usize, usize) {
        let weight_cache = self.weight_cache.read();
        let mmap_cache = self.mmap_cache.read();
        (weight_cache.len(), mmap_cache.len())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_loader_creation() {
        let config = LoaderConfig::default();
        let loader = ModelLoader::new(config);
        assert!(loader.is_ok());
    }

    #[test]
    fn test_cache_operations() {
        let loader = ModelLoader::default().unwrap();
        loader.clear_cache();
        let (weight_count, mmap_count) = loader.cache_stats();
        assert_eq!(weight_count, 0);
        assert_eq!(mmap_count, 0);
    }
}
