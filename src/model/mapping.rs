//! Weight mapping and transformation system.
//!
//! This module provides functionality for mapping and transforming weights between
//! different model formats and frameworks.

use std::collections::HashMap;
use serde::{Deserialize, Serialize};

use crate::error::{<PERSON>Error, ErrorContext};
use crate::tensor::{Tensor, Numeric, <PERSON>hape, TensorOps, cpu::CpuTensor};

/// Weight mapping configuration.
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct WeightMapping {
    /// Source weight name.
    pub source_name: String,
    /// Target weight name.
    pub target_name: String,
    /// Optional transformation to apply.
    pub transform: Option<WeightTransform>,
}

/// Weight transformation operations.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WeightTransform {
    /// Transpose dimensions.
    Transpose(Vec<usize>),
    /// Reshape to new shape.
    Reshape(Vec<usize>),
    /// Split tensor along dimension.
    Split { dim: usize, chunks: usize },
    /// Concatenate tensors along dimension.
    Concat { dim: usize },
    /// Scale values by a factor.
    Scale(f32),
    /// Add a constant value.
    Add(f32),
    /// Convert data type.
    ConvertType(String),
}

/// Weight mapper for transforming weights between formats.
pub struct WeightMapper {
    /// Mapping rules.
    mappings: HashMap<String, WeightMapping>,
    /// Architecture-specific mapping sets.
    architecture_mappings: HashMap<String, HashMap<String, WeightMapping>>,
}

impl WeightMapper {
    /// Create a new weight mapper.
    pub fn new() -> Self {
        Self {
            mappings: HashMap::new(),
            architecture_mappings: HashMap::new(),
        }
    }

    /// Add a weight mapping rule.
    pub fn add_mapping(&mut self, mapping: WeightMapping) {
        self.mappings.insert(mapping.source_name.clone(), mapping);
    }

    /// Add architecture-specific mappings.
    pub fn add_architecture_mappings(
        &mut self,
        architecture: String,
        mappings: HashMap<String, WeightMapping>,
    ) {
        self.architecture_mappings.insert(architecture, mappings);
    }

    /// Map weights from source format to target format.
    pub fn map_weights<T: Numeric>(
        &self,
        source_weights: HashMap<String, CpuTensor<T>>,
        target_architecture: &str,
    ) -> Result<HashMap<String, CpuTensor<T>>, ModelError> {
        let mut mapped_weights = HashMap::new();

        // Get architecture-specific mappings if available
        let arch_mappings = self.architecture_mappings.get(target_architecture);

        for (source_name, tensor) in source_weights {
            // Try architecture-specific mapping first
            let mapping = if let Some(arch_mappings) = arch_mappings {
                arch_mappings.get(&source_name)
            } else {
                None
            }.or_else(|| self.mappings.get(&source_name));

            if let Some(mapping) = mapping {
                let transformed_tensor = self.apply_transform(&tensor, &mapping.transform)?;
                mapped_weights.insert(mapping.target_name.clone(), transformed_tensor);
            } else {
                // Try automatic mapping
                if let Some(target_name) = self.auto_map_name(&source_name, target_architecture) {
                    mapped_weights.insert(target_name, tensor);
                } else {
                    // Keep original name if no mapping found
                    mapped_weights.insert(source_name, tensor);
                }
            }
        }

        Ok(mapped_weights)
    }

    /// Apply transformation to a tensor.
    fn apply_transform<T: Numeric>(
        &self,
        tensor: &CpuTensor<T>,
        transform: &Option<WeightTransform>,
    ) -> Result<CpuTensor<T>, ModelError> {
        match transform {
            Some(WeightTransform::Transpose(dims)) => {
                if dims.len() == 2 {
                    tensor.transpose(dims[0], dims[1]).map_err(|e| ModelError::Tensor(e))
                } else {
                    // For now, only support 2D transpose
                    Err(ModelError::InvalidConfiguration {
                        message: "Only 2D transpose is currently supported".to_string(),
                        config_field: Some("transpose_dims".to_string()),
                        context: Some(ErrorContext::new("apply_transform", "model::mapping")),
                    })
                }
            }
            Some(WeightTransform::Reshape(shape)) => {
                let new_shape = Shape::new(shape.clone());
                tensor.reshape(&new_shape).map_err(|e| ModelError::Tensor(e))
            }
            Some(WeightTransform::Split { dim, chunks }) => {
                self.split_tensor(tensor, *dim, *chunks)
            }
            Some(WeightTransform::Scale(factor)) => {
                tensor.mul_scalar(T::from_f32(*factor)).map_err(|e| ModelError::Tensor(e))
            }
            Some(WeightTransform::Add(value)) => {
                tensor.add_scalar(T::from_f32(*value)).map_err(|e| ModelError::Tensor(e))
            }
            Some(WeightTransform::ConvertType(_dtype)) => {
                // Type conversion would require different generic parameter
                // For now, return the original tensor
                Ok(tensor.clone())
            }
            None => Ok(tensor.clone()),
            _ => Err(ModelError::UnsupportedArchitecture {
                architecture: "transform_not_implemented".to_string(),
                supported_architectures: vec![],
                context: Some(ErrorContext::new("apply_transform", "model::mapping")),
            }),
        }
    }

    /// Split tensor along a dimension.
    fn split_tensor<T: Numeric>(
        &self,
        tensor: &CpuTensor<T>,
        dim: usize,
        chunks: usize,
    ) -> Result<CpuTensor<T>, ModelError> {
        let shape = tensor.shape();
        if dim >= shape.rank() {
            return Err(ModelError::InvalidConfiguration {
                message: format!("Split dimension {} out of bounds for tensor with rank {}", dim, shape.rank()),
                config_field: Some("split_dimension".to_string()),
                context: Some(ErrorContext::new("split_tensor", "model::mapping")),
            });
        }

        let dim_size = shape.dims()[dim];
        if dim_size % chunks != 0 {
            return Err(ModelError::InvalidConfiguration {
                message: format!("Cannot split dimension {} of size {} into {} equal chunks", dim, dim_size, chunks),
                config_field: Some("split_chunks".to_string()),
                context: Some(ErrorContext::new("split_tensor", "model::mapping")),
            });
        }

        // For now, return the first chunk
        // A full implementation would return all chunks
        let chunk_size = dim_size / chunks;
        let mut new_dims = shape.dims().to_vec();
        new_dims[dim] = chunk_size;
        let new_shape = Shape::new(new_dims);

        // This is a simplified implementation
        // In practice, you'd need to slice the tensor properly
        tensor.reshape(&new_shape).map_err(|e| ModelError::Tensor(e))
    }

    /// Attempt automatic name mapping.
    fn auto_map_name(&self, source_name: &str, target_architecture: &str) -> Option<String> {
        // Common mapping patterns
        let mappings = match target_architecture {
            "gpt" | "gpt2" | "gpt-neo" | "gpt-j" => {
                self.get_gpt_mappings()
            }
            "bert" | "roberta" | "deberta" => {
                self.get_bert_mappings()
            }
            "llama" | "alpaca" | "vicuna" => {
                self.get_llama_mappings()
            }
            _ => HashMap::new(),
        };

        // Try exact match first
        if let Some(target) = mappings.get(source_name) {
            return Some(target.clone());
        }

        // Try pattern matching
        for (pattern, replacement) in mappings.iter() {
            if source_name.contains(pattern) {
                return Some(source_name.replace(pattern, replacement));
            }
        }

        None
    }

    /// Get GPT-style weight name mappings.
    fn get_gpt_mappings(&self) -> HashMap<String, String> {
        let mut mappings = HashMap::new();
        
        // Common PyTorch to HuggingFace mappings
        mappings.insert("transformer.wte.weight".to_string(), "wte.weight".to_string());
        mappings.insert("transformer.wpe.weight".to_string(), "wpe.weight".to_string());
        mappings.insert("transformer.ln_f.weight".to_string(), "ln_f.weight".to_string());
        mappings.insert("transformer.ln_f.bias".to_string(), "ln_f.bias".to_string());
        mappings.insert("lm_head.weight".to_string(), "lm_head.weight".to_string());
        
        // Layer-specific patterns would be handled separately
        mappings
    }

    /// Get BERT-style weight name mappings.
    fn get_bert_mappings(&self) -> HashMap<String, String> {
        let mut mappings = HashMap::new();
        
        mappings.insert("bert.embeddings.word_embeddings.weight".to_string(), "embeddings.word_embeddings.weight".to_string());
        mappings.insert("bert.embeddings.position_embeddings.weight".to_string(), "embeddings.position_embeddings.weight".to_string());
        mappings.insert("bert.embeddings.token_type_embeddings.weight".to_string(), "embeddings.token_type_embeddings.weight".to_string());
        mappings.insert("bert.embeddings.LayerNorm.weight".to_string(), "embeddings.LayerNorm.weight".to_string());
        mappings.insert("bert.embeddings.LayerNorm.bias".to_string(), "embeddings.LayerNorm.bias".to_string());
        
        mappings
    }

    /// Get LLaMA-style weight name mappings.
    fn get_llama_mappings(&self) -> HashMap<String, String> {
        let mut mappings = HashMap::new();
        
        mappings.insert("model.embed_tokens.weight".to_string(), "embed_tokens.weight".to_string());
        mappings.insert("model.norm.weight".to_string(), "norm.weight".to_string());
        mappings.insert("lm_head.weight".to_string(), "lm_head.weight".to_string());
        
        mappings
    }

    /// Load mappings from a configuration file.
    pub fn load_mappings_from_file(&mut self, path: &std::path::Path) -> Result<(), ModelError> {
        let content = std::fs::read_to_string(path).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: path.to_path_buf(),
                io_error: Some(e),
                context: Some(ErrorContext::new("load_mappings", "model::mapping")),
            }
        })?;

        let mappings: HashMap<String, WeightMapping> = serde_json::from_str(&content).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to parse mapping file: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("parse_mappings", "model::mapping")),
            }
        })?;

        for (_, mapping) in mappings {
            self.add_mapping(mapping);
        }

        Ok(())
    }

    /// Save mappings to a configuration file.
    pub fn save_mappings_to_file(&self, path: &std::path::Path) -> Result<(), ModelError> {
        let content = serde_json::to_string_pretty(&self.mappings).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to serialize mappings: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("serialize_mappings", "model::mapping")),
            }
        })?;

        std::fs::write(path, content).map_err(|e| {
            ModelError::WeightLoadingFailed {
                path: path.to_path_buf(),
                io_error: Some(e),
                context: Some(ErrorContext::new("save_mappings", "model::mapping")),
            }
        })
    }

    /// Validate that all required weights are present.
    pub fn validate_weights<T: Numeric>(
        &self,
        weights: &HashMap<String, CpuTensor<T>>,
        required_weights: &[String],
    ) -> Result<(), ModelError> {
        let missing_weights: Vec<String> = required_weights
            .iter()
            .filter(|&name| !weights.contains_key(name))
            .cloned()
            .collect();

        if !missing_weights.is_empty() {
            return Err(ModelError::ParameterMissing {
                parameter_name: missing_weights.join(", "),
                context: Some(ErrorContext::new("validate_weights", "model::mapping")),
            });
        }

        Ok(())
    }

    /// Get statistics about the mapping process.
    pub fn get_mapping_stats(&self) -> MappingStats {
        MappingStats {
            total_mappings: self.mappings.len(),
            architecture_mappings: self.architecture_mappings.len(),
            transform_types: self.get_transform_type_counts(),
        }
    }

    /// Get counts of different transform types.
    fn get_transform_type_counts(&self) -> HashMap<String, usize> {
        let mut counts = HashMap::new();
        
        for mapping in self.mappings.values() {
            let transform_type = match &mapping.transform {
                Some(WeightTransform::Transpose(_)) => "transpose",
                Some(WeightTransform::Reshape(_)) => "reshape",
                Some(WeightTransform::Split { .. }) => "split",
                Some(WeightTransform::Concat { .. }) => "concat",
                Some(WeightTransform::Scale(_)) => "scale",
                Some(WeightTransform::Add(_)) => "add",
                Some(WeightTransform::ConvertType(_)) => "convert_type",
                None => "none",
            };
            
            *counts.entry(transform_type.to_string()).or_insert(0) += 1;
        }
        
        counts
    }
}

impl Default for WeightMapper {
    fn default() -> Self {
        Self::new()
    }
}

/// Statistics about weight mapping.
#[derive(Debug, Clone)]
pub struct MappingStats {
    /// Total number of mappings.
    pub total_mappings: usize,
    /// Number of architecture-specific mapping sets.
    pub architecture_mappings: usize,
    /// Count of different transform types.
    pub transform_types: HashMap<String, usize>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::{CpuTensor, TensorFactory};

    #[test]
    fn test_weight_mapper_creation() {
        let mapper = WeightMapper::new();
        assert_eq!(mapper.mappings.len(), 0);
    }

    #[test]
    fn test_add_mapping() {
        let mut mapper = WeightMapper::new();
        let mapping = WeightMapping {
            source_name: "source".to_string(),
            target_name: "target".to_string(),
            transform: None,
        };
        
        mapper.add_mapping(mapping);
        assert_eq!(mapper.mappings.len(), 1);
    }

    #[test]
    fn test_auto_mapping() {
        let mapper = WeightMapper::new();
        
        // Test GPT mapping
        let result = mapper.auto_map_name("transformer.wte.weight", "gpt2");
        assert_eq!(result, Some("wte.weight".to_string()));
        
        // Test unknown mapping
        let result = mapper.auto_map_name("unknown.weight", "unknown_arch");
        assert_eq!(result, None);
    }

    #[test]
    fn test_mapping_stats() {
        let mut mapper = WeightMapper::new();
        let mapping = WeightMapping {
            source_name: "test".to_string(),
            target_name: "test_target".to_string(),
            transform: Some(WeightTransform::Transpose(vec![0, 1])),
        };
        
        mapper.add_mapping(mapping);
        let stats = mapper.get_mapping_stats();
        assert_eq!(stats.total_mappings, 1);
        assert_eq!(stats.transform_types.get("transpose"), Some(&1));
    }
}
