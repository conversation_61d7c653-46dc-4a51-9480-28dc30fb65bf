//! Model registry and factory system.
//!
//! This module provides a registry for different model architectures and factories
//! for creating model instances based on configuration.

use std::collections::HashMap;
use std::sync::Arc;

use crate::error::{ModelError, ErrorContext};
use crate::tensor::{Tensor, Numeric, cpu::CpuTensor};
use super::config::ModelConfig;

/// Trait for model instances.
pub trait Model<T: Numeric>: Send + Sync {
    /// Get the model configuration.
    fn config(&self) -> &ModelConfig;
    
    /// Load weights into the model.
    fn load_weights(&mut self, weights: HashMap<String, CpuTensor<T>>) -> Result<(), ModelError>;
    
    /// Get the number of parameters in the model.
    fn num_parameters(&self) -> usize;
    
    /// Get the model's memory usage in bytes.
    fn memory_usage(&self) -> usize;
    
    /// Perform forward pass.
    fn forward(&self, input: &CpuTensor<T>) -> Result<CpuTensor<T>, ModelError>;
    
    /// Get model metadata.
    fn metadata(&self) -> ModelMetadata;
}

/// Model metadata information.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, serde::Serialize, serde::Deserialize)]
pub struct ModelMetadata {
    /// Model name.
    pub name: String,
    /// Model version.
    pub version: String,
    /// Model architecture.
    pub architecture: String,
    /// Number of parameters.
    pub num_parameters: usize,
    /// Memory usage in bytes.
    pub memory_usage: usize,
    /// Supported data types.
    pub supported_dtypes: Vec<String>,
    /// Additional metadata.
    pub extra_info: HashMap<String, String>,
}

/// Trait for model factories.
pub trait ModelFactory: Send + Sync {
    /// Get supported architectures.
    fn supported_architectures(&self) -> Vec<&str>;

    /// Check if an architecture is supported.
    fn supports_architecture(&self, architecture: &str) -> bool {
        self.supported_architectures().contains(&architecture)
    }

    /// Get factory metadata.
    fn factory_info(&self) -> FactoryInfo;

    /// Create a model instance from configuration (f32).
    fn create_model_f32(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, CpuTensor<f32>>>,
    ) -> Result<Box<dyn Model<f32>>, ModelError>;

    /// Create a model instance from configuration (f64).
    fn create_model_f64(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, CpuTensor<f64>>>,
    ) -> Result<Box<dyn Model<f64>>, ModelError>;
}

/// Factory information.
#[derive(Debug, Clone)]
pub struct FactoryInfo {
    /// Factory name.
    pub name: String,
    /// Factory version.
    pub version: String,
    /// Supported architectures.
    pub supported_architectures: Vec<String>,
    /// Description.
    pub description: String,
}

/// Model registry for managing different model architectures.
pub struct ModelRegistry {
    /// Registered model factories.
    factories: HashMap<String, Arc<dyn ModelFactory>>,
    /// Architecture aliases.
    aliases: HashMap<String, String>,
}

impl ModelRegistry {
    /// Create a new model registry.
    pub fn new() -> Self {
        Self {
            factories: HashMap::new(),
            aliases: HashMap::new(),
        }
    }

    /// Register a model factory.
    pub fn register_factory<F: ModelFactory + 'static>(
        &mut self,
        name: String,
        factory: F,
    ) -> Result<(), ModelError> {
        let factory = Arc::new(factory);
        
        // Register the factory
        self.factories.insert(name.clone(), factory.clone());
        
        // Register all supported architectures
        for arch in factory.supported_architectures() {
            self.aliases.insert(arch.to_string(), name.clone());
        }
        
        Ok(())
    }

    /// Create a model from configuration (f32).
    pub fn create_model_f32(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, CpuTensor<f32>>>,
    ) -> Result<Box<dyn Model<f32>>, ModelError> {
        let factory_name = self.resolve_architecture(&config.architecture)?;

        let factory = self.factories.get(&factory_name)
            .ok_or_else(|| ModelError::UnsupportedArchitecture {
                architecture: config.architecture.clone(),
                supported_architectures: self.list_supported_architectures(),
                context: Some(ErrorContext::new("create_model_f32", "model::registry")),
            })?;

        factory.create_model_f32(config, weights)
    }

    /// Create a model from configuration (f64).
    pub fn create_model_f64(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, CpuTensor<f64>>>,
    ) -> Result<Box<dyn Model<f64>>, ModelError> {
        let factory_name = self.resolve_architecture(&config.architecture)?;

        let factory = self.factories.get(&factory_name)
            .ok_or_else(|| ModelError::UnsupportedArchitecture {
                architecture: config.architecture.clone(),
                supported_architectures: self.list_supported_architectures(),
                context: Some(ErrorContext::new("create_model_f64", "model::registry")),
            })?;

        factory.create_model_f64(config, weights)
    }

    /// Resolve architecture name to factory name.
    fn resolve_architecture(&self, architecture: &str) -> Result<String, ModelError> {
        self.aliases.get(architecture)
            .cloned()
            .ok_or_else(|| ModelError::UnsupportedArchitecture {
                architecture: architecture.to_string(),
                supported_architectures: self.list_supported_architectures(),
                context: Some(ErrorContext::new("resolve_architecture", "model::registry")),
            })
    }

    /// List all supported architectures.
    pub fn list_supported_architectures(&self) -> Vec<String> {
        self.aliases.keys().cloned().collect()
    }

    /// List all registered factories.
    pub fn list_factories(&self) -> Vec<String> {
        self.factories.keys().cloned().collect()
    }

    /// Get factory information.
    pub fn get_factory_info(&self, name: &str) -> Option<FactoryInfo> {
        self.factories.get(name).map(|f| f.factory_info())
    }

    /// Auto-detect model architecture from configuration.
    pub fn detect_architecture(&self, config: &ModelConfig) -> Option<String> {
        // Try exact match first
        if self.aliases.contains_key(&config.architecture) {
            return Some(config.architecture.clone());
        }
        
        // Try model_type
        if self.aliases.contains_key(&config.model_type) {
            return Some(config.model_type.clone());
        }
        
        // Try pattern matching
        let arch_lower = config.architecture.to_lowercase();
        let model_type_lower = config.model_type.to_lowercase();
        
        for supported_arch in self.list_supported_architectures() {
            let supported_lower = supported_arch.to_lowercase();
            if arch_lower.contains(&supported_lower) || 
               model_type_lower.contains(&supported_lower) ||
               supported_lower.contains(&arch_lower) ||
               supported_lower.contains(&model_type_lower) {
                return Some(supported_arch);
            }
        }
        
        None
    }
}

impl Default for ModelRegistry {
    fn default() -> Self {
        let mut registry = Self::new();
        
        // Register built-in factories
        registry.register_factory("gpt".to_string(), GPTModelFactory::new()).ok();
        registry.register_factory("transformer".to_string(), TransformerModelFactory::new()).ok();
        
        registry
    }
}

/// GPT model factory.
pub struct GPTModelFactory {
    info: FactoryInfo,
}

impl GPTModelFactory {
    pub fn new() -> Self {
        Self {
            info: FactoryInfo {
                name: "GPT Factory".to_string(),
                version: "1.0.0".to_string(),
                supported_architectures: vec![
                    "gpt".to_string(),
                    "gpt2".to_string(),
                    "gpt-neo".to_string(),
                    "gpt-j".to_string(),
                    "gpt-neox".to_string(),
                ],
                description: "Factory for GPT-style autoregressive language models".to_string(),
            },
        }
    }
}

impl ModelFactory for GPTModelFactory {
    fn supported_architectures(&self) -> Vec<&str> {
        self.info.supported_architectures.iter().map(|s| s.as_str()).collect()
    }

    fn factory_info(&self) -> FactoryInfo {
        self.info.clone()
    }

    fn create_model_f32(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, CpuTensor<f32>>>,
    ) -> Result<Box<dyn Model<f32>>, ModelError> {
        let mut model = GPTModel::new(config.clone())?;

        if let Some(weights) = weights {
            model.load_weights(weights)?;
        }

        Ok(Box::new(model))
    }

    fn create_model_f64(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, CpuTensor<f64>>>,
    ) -> Result<Box<dyn Model<f64>>, ModelError> {
        let mut model = GPTModel::new(config.clone())?;

        if let Some(weights) = weights {
            model.load_weights(weights)?;
        }

        Ok(Box::new(model))
    }
}

/// Generic transformer model factory.
pub struct TransformerModelFactory {
    info: FactoryInfo,
}

impl TransformerModelFactory {
    pub fn new() -> Self {
        Self {
            info: FactoryInfo {
                name: "Transformer Factory".to_string(),
                version: "1.0.0".to_string(),
                supported_architectures: vec![
                    "transformer".to_string(),
                    "bert".to_string(),
                    "roberta".to_string(),
                    "deberta".to_string(),
                ],
                description: "Factory for generic transformer models".to_string(),
            },
        }
    }
}

impl ModelFactory for TransformerModelFactory {
    fn supported_architectures(&self) -> Vec<&str> {
        self.info.supported_architectures.iter().map(|s| s.as_str()).collect()
    }

    fn factory_info(&self) -> FactoryInfo {
        self.info.clone()
    }

    fn create_model_f32(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, CpuTensor<f32>>>,
    ) -> Result<Box<dyn Model<f32>>, ModelError> {
        let mut model = TransformerModel::new(config.clone())?;

        if let Some(weights) = weights {
            model.load_weights(weights)?;
        }

        Ok(Box::new(model))
    }

    fn create_model_f64(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, CpuTensor<f64>>>,
    ) -> Result<Box<dyn Model<f64>>, ModelError> {
        let mut model = TransformerModel::new(config.clone())?;

        if let Some(weights) = weights {
            model.load_weights(weights)?;
        }

        Ok(Box::new(model))
    }
}

/// Simple GPT model implementation.
pub struct GPTModel<T: Numeric> {
    config: ModelConfig,
    weights: Option<HashMap<String, CpuTensor<T>>>,
}

impl<T: Numeric> GPTModel<T> {
    pub fn new(config: ModelConfig) -> Result<Self, ModelError> {
        config.validate()?;
        Ok(Self {
            config,
            weights: None,
        })
    }
}

impl<T: Numeric> Model<T> for GPTModel<T> {
    fn config(&self) -> &ModelConfig {
        &self.config
    }
    
    fn load_weights(&mut self, weights: HashMap<String, CpuTensor<T>>) -> Result<(), ModelError> {
        // Validate weights match model configuration
        // This is a simplified implementation
        self.weights = Some(weights);
        Ok(())
    }
    
    fn num_parameters(&self) -> usize {
        // Simplified calculation
        let vocab_size = self.config.vocab_size;
        let hidden_size = self.config.hidden_size;
        let num_layers = self.config.num_layers;
        let intermediate_size = self.config.get_intermediate_size();
        
        // Embedding parameters
        let embedding_params = vocab_size * hidden_size;
        
        // Transformer layer parameters (simplified)
        let layer_params = num_layers * (
            4 * hidden_size * hidden_size + // attention weights
            2 * hidden_size * intermediate_size + // feed-forward weights
            4 * hidden_size // biases and layer norms
        );
        
        embedding_params + layer_params
    }
    
    fn memory_usage(&self) -> usize {
        // Simplified calculation: assume 4 bytes per parameter (f32)
        self.num_parameters() * 4
    }
    
    fn forward(&self, input: &CpuTensor<T>) -> Result<CpuTensor<T>, ModelError> {
        // This is a placeholder implementation
        // In a real implementation, this would perform the actual forward pass
        Err(ModelError::UnsupportedArchitecture {
            architecture: "forward_not_implemented".to_string(),
            supported_architectures: vec![],
            context: Some(ErrorContext::new("forward", "model::registry")),
        })
    }
    
    fn metadata(&self) -> ModelMetadata {
        ModelMetadata {
            name: format!("GPT-{}", self.config.model_type),
            version: "1.0.0".to_string(),
            architecture: self.config.architecture.clone(),
            num_parameters: self.num_parameters(),
            memory_usage: self.memory_usage(),
            supported_dtypes: vec!["f32".to_string(), "f64".to_string()],
            extra_info: HashMap::new(),
        }
    }
}

/// Simple transformer model implementation.
pub struct TransformerModel<T: Numeric> {
    config: ModelConfig,
    weights: Option<HashMap<String, CpuTensor<T>>>,
}

impl<T: Numeric> TransformerModel<T> {
    pub fn new(config: ModelConfig) -> Result<Self, ModelError> {
        config.validate()?;
        Ok(Self {
            config,
            weights: None,
        })
    }
}

impl<T: Numeric> Model<T> for TransformerModel<T> {
    fn config(&self) -> &ModelConfig {
        &self.config
    }
    
    fn load_weights(&mut self, weights: HashMap<String, CpuTensor<T>>) -> Result<(), ModelError> {
        self.weights = Some(weights);
        Ok(())
    }
    
    fn num_parameters(&self) -> usize {
        // Similar to GPT but may have different architecture
        let vocab_size = self.config.vocab_size;
        let hidden_size = self.config.hidden_size;
        let num_layers = self.config.num_layers;
        let intermediate_size = self.config.get_intermediate_size();
        
        vocab_size * hidden_size + num_layers * (4 * hidden_size * hidden_size + 2 * hidden_size * intermediate_size + 4 * hidden_size)
    }
    
    fn memory_usage(&self) -> usize {
        self.num_parameters() * 4
    }
    
    fn forward(&self, input: &CpuTensor<T>) -> Result<CpuTensor<T>, ModelError> {
        // Placeholder implementation
        Err(ModelError::UnsupportedArchitecture {
            architecture: "forward_not_implemented".to_string(),
            supported_architectures: vec![],
            context: Some(ErrorContext::new("forward", "model::registry")),
        })
    }
    
    fn metadata(&self) -> ModelMetadata {
        ModelMetadata {
            name: format!("Transformer-{}", self.config.model_type),
            version: "1.0.0".to_string(),
            architecture: self.config.architecture.clone(),
            num_parameters: self.num_parameters(),
            memory_usage: self.memory_usage(),
            supported_dtypes: vec!["f32".to_string(), "f64".to_string()],
            extra_info: HashMap::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_registry_creation() {
        let registry = ModelRegistry::new();
        assert!(registry.list_factories().is_empty());
    }

    #[test]
    fn test_default_registry() {
        let registry = ModelRegistry::default();
        let architectures = registry.list_supported_architectures();
        assert!(architectures.contains(&"gpt".to_string()));
        assert!(architectures.contains(&"transformer".to_string()));
    }

    #[test]
    fn test_architecture_detection() {
        let registry = ModelRegistry::default();
        let mut config = ModelConfig::default();
        
        config.architecture = "gpt2".to_string();
        assert!(registry.detect_architecture(&config).is_some());
        
        config.architecture = "unknown".to_string();
        config.model_type = "gpt".to_string();
        assert!(registry.detect_architecture(&config).is_some());
    }
}
