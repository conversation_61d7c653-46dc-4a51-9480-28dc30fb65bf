//! Model configuration system.
//!
//! This module provides configuration structures and parsing for various model architectures.

use std::collections::HashMap;
use std::path::Path;
use serde::{Deserialize, Serialize};

use crate::error::{ModelError, ErrorContext};

/// Model configuration structure.
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelConfig {
    /// Model architecture type.
    pub architecture: String,
    /// Model type (e.g., "gpt2", "bert", "llama").
    pub model_type: String,
    /// Vocabulary size.
    pub vocab_size: usize,
    /// Hidden dimension size.
    pub hidden_size: usize,
    /// Number of transformer layers.
    pub num_layers: usize,
    /// Number of attention heads.
    pub num_attention_heads: usize,
    /// Intermediate size in feed-forward networks.
    pub intermediate_size: Option<usize>,
    /// Maximum position embeddings.
    pub max_position_embeddings: usize,
    /// Layer normalization epsilon.
    pub layer_norm_eps: f32,
    /// Dropout probability.
    pub dropout: f32,
    /// Activation function name.
    pub activation_function: String,
    /// Whether to use cache during inference.
    pub use_cache: bool,
    /// PyTorch data type string.
    pub torch_dtype: String,
    /// Additional architecture-specific parameters.
    #[serde(flatten)]
    pub extra_params: HashMap<String, serde_json::Value>,
}

impl Default for ModelConfig {
    fn default() -> Self {
        Self {
            architecture: "transformer".to_string(),
            model_type: "gpt2".to_string(),
            vocab_size: 50257,
            hidden_size: 768,
            num_layers: 12,
            num_attention_heads: 12,
            intermediate_size: Some(3072),
            max_position_embeddings: 1024,
            layer_norm_eps: 1e-5,
            dropout: 0.1,
            activation_function: "gelu".to_string(),
            use_cache: true,
            torch_dtype: "float32".to_string(),
            extra_params: HashMap::new(),
        }
    }
}

impl ModelConfig {
    /// Load configuration from a JSON file.
    pub fn from_json(path: &Path) -> Result<Self, ModelError> {
        let content = std::fs::read_to_string(path).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to read config file: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("read_config_file", "model::config")),
            }
        })?;

        let config: Self = serde_json::from_str(&content).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to parse JSON config: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("parse_json_config", "model::config")),
            }
        })?;

        config.validate()?;
        Ok(config)
    }

    /// Load configuration from a YAML file.
    pub fn from_yaml(path: &Path) -> Result<Self, ModelError> {
        let content = std::fs::read_to_string(path).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to read config file: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("read_config_file", "model::config")),
            }
        })?;

        let config: Self = serde_yaml::from_str(&content).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to parse YAML config: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("parse_yaml_config", "model::config")),
            }
        })?;

        config.validate()?;
        Ok(config)
    }

    /// Load configuration from a TOML file.
    pub fn from_toml(path: &Path) -> Result<Self, ModelError> {
        let content = std::fs::read_to_string(path).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to read config file: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("read_config_file", "model::config")),
            }
        })?;

        let config: Self = toml::from_str(&content).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to parse TOML config: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("parse_toml_config", "model::config")),
            }
        })?;

        config.validate()?;
        Ok(config)
    }

    /// Load configuration from any supported format based on file extension.
    pub fn from_file(path: &Path) -> Result<Self, ModelError> {
        match path.extension().and_then(|ext| ext.to_str()) {
            Some("json") => Self::from_json(path),
            Some("yaml") | Some("yml") => Self::from_yaml(path),
            Some("toml") => Self::from_toml(path),
            _ => Err(ModelError::InvalidConfiguration {
                message: "Unsupported config file format. Supported: .json, .yaml, .yml, .toml".to_string(),
                config_field: None,
                context: Some(ErrorContext::new("unsupported_format", "model::config")),
            }),
        }
    }

    /// Convert from HuggingFace configuration format.
    pub fn from_hf_config(hf_config: &HuggingFaceConfig) -> Result<Self, ModelError> {
        let mut config = Self {
            architecture: hf_config.architectures.first()
                .unwrap_or(&"transformer".to_string())
                .clone(),
            model_type: hf_config.model_type.clone(),
            vocab_size: hf_config.vocab_size,
            hidden_size: hf_config.hidden_size.unwrap_or(hf_config.n_embd.unwrap_or(768)),
            num_layers: hf_config.num_hidden_layers.unwrap_or(hf_config.n_layer.unwrap_or(12)),
            num_attention_heads: hf_config.num_attention_heads.unwrap_or(hf_config.n_head.unwrap_or(12)),
            intermediate_size: hf_config.intermediate_size.or(hf_config.n_inner),
            max_position_embeddings: hf_config.max_position_embeddings.unwrap_or(hf_config.n_positions.unwrap_or(1024)),
            layer_norm_eps: hf_config.layer_norm_epsilon.unwrap_or(hf_config.layer_norm_eps.unwrap_or(1e-5)),
            dropout: hf_config.dropout.unwrap_or(hf_config.resid_pdrop.unwrap_or(0.1)),
            activation_function: hf_config.activation_function.clone().unwrap_or("gelu".to_string()),
            use_cache: hf_config.use_cache.unwrap_or(true),
            torch_dtype: hf_config.torch_dtype.clone().unwrap_or("float32".to_string()),
            extra_params: HashMap::new(),
        };

        // Add any extra parameters
        if let Some(ref extra) = hf_config.extra_params {
            config.extra_params = extra.clone();
        }

        config.validate()?;
        Ok(config)
    }

    /// Save configuration to a JSON file.
    pub fn save_json(&self, path: &Path) -> Result<(), ModelError> {
        let content = serde_json::to_string_pretty(self).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to serialize config: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("serialize_config", "model::config")),
            }
        })?;

        std::fs::write(path, content).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to write config file: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("write_config_file", "model::config")),
            }
        })
    }

    /// Save configuration to a YAML file.
    pub fn save_yaml(&self, path: &Path) -> Result<(), ModelError> {
        let content = serde_yaml::to_string(self).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to serialize config: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("serialize_config", "model::config")),
            }
        })?;

        std::fs::write(path, content).map_err(|e| {
            ModelError::InvalidConfiguration {
                message: format!("Failed to write config file: {}", e),
                config_field: None,
                context: Some(ErrorContext::new("write_config_file", "model::config")),
            }
        })
    }

    /// Validate the configuration.
    pub fn validate(&self) -> Result<(), ModelError> {
        if self.vocab_size == 0 {
            return Err(ModelError::InvalidConfiguration {
                message: "vocab_size must be greater than 0".to_string(),
                config_field: Some("vocab_size".to_string()),
                context: Some(ErrorContext::new("validate_config", "model::config")),
            });
        }

        if self.hidden_size == 0 {
            return Err(ModelError::InvalidConfiguration {
                message: "hidden_size must be greater than 0".to_string(),
                config_field: Some("hidden_size".to_string()),
                context: Some(ErrorContext::new("validate_config", "model::config")),
            });
        }

        if self.num_layers == 0 {
            return Err(ModelError::InvalidConfiguration {
                message: "num_layers must be greater than 0".to_string(),
                config_field: Some("num_layers".to_string()),
                context: Some(ErrorContext::new("validate_config", "model::config")),
            });
        }

        if self.num_attention_heads == 0 {
            return Err(ModelError::InvalidConfiguration {
                message: "num_attention_heads must be greater than 0".to_string(),
                config_field: Some("num_attention_heads".to_string()),
                context: Some(ErrorContext::new("validate_config", "model::config")),
            });
        }

        if self.hidden_size % self.num_attention_heads != 0 {
            return Err(ModelError::InvalidConfiguration {
                message: format!(
                    "hidden_size ({}) must be divisible by num_attention_heads ({})",
                    self.hidden_size, self.num_attention_heads
                ),
                config_field: Some("hidden_size".to_string()),
                context: Some(ErrorContext::new("validate_config", "model::config")),
            });
        }

        if self.layer_norm_eps <= 0.0 {
            return Err(ModelError::InvalidConfiguration {
                message: "layer_norm_eps must be greater than 0".to_string(),
                config_field: Some("layer_norm_eps".to_string()),
                context: Some(ErrorContext::new("validate_config", "model::config")),
            });
        }

        if self.dropout < 0.0 || self.dropout > 1.0 {
            return Err(ModelError::InvalidConfiguration {
                message: "dropout must be between 0.0 and 1.0".to_string(),
                config_field: Some("dropout".to_string()),
                context: Some(ErrorContext::new("validate_config", "model::config")),
            });
        }

        Ok(())
    }

    /// Get the head dimension.
    pub fn head_dim(&self) -> usize {
        self.hidden_size / self.num_attention_heads
    }

    /// Get the intermediate size (defaults to 4 * hidden_size if not specified).
    pub fn get_intermediate_size(&self) -> usize {
        self.intermediate_size.unwrap_or(4 * self.hidden_size)
    }
}

/// HuggingFace configuration format for compatibility.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HuggingFaceConfig {
    pub architectures: Vec<String>,
    pub model_type: String,
    pub vocab_size: usize,
    pub hidden_size: Option<usize>,
    pub n_embd: Option<usize>,
    pub num_hidden_layers: Option<usize>,
    pub n_layer: Option<usize>,
    pub num_attention_heads: Option<usize>,
    pub n_head: Option<usize>,
    pub intermediate_size: Option<usize>,
    pub n_inner: Option<usize>,
    pub max_position_embeddings: Option<usize>,
    pub n_positions: Option<usize>,
    pub layer_norm_epsilon: Option<f32>,
    pub layer_norm_eps: Option<f32>,
    pub dropout: Option<f32>,
    pub resid_pdrop: Option<f32>,
    pub activation_function: Option<String>,
    pub use_cache: Option<bool>,
    pub torch_dtype: Option<String>,
    #[serde(flatten)]
    pub extra_params: Option<HashMap<String, serde_json::Value>>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_default_config() {
        let config = ModelConfig::default();
        assert!(config.validate().is_ok());
        assert_eq!(config.head_dim(), 64); // 768 / 12
        assert_eq!(config.get_intermediate_size(), 3072);
    }

    #[test]
    fn test_config_validation() {
        let mut config = ModelConfig::default();
        
        // Test invalid vocab_size
        config.vocab_size = 0;
        assert!(config.validate().is_err());
        
        // Test invalid hidden_size / num_attention_heads
        config = ModelConfig::default();
        config.hidden_size = 100;
        config.num_attention_heads = 7;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_config_serialization() {
        let config = ModelConfig::default();
        let json_str = serde_json::to_string(&config).unwrap();
        let deserialized: ModelConfig = serde_json::from_str(&json_str).unwrap();
        assert_eq!(config.vocab_size, deserialized.vocab_size);
    }
}
