//! Model management and configuration.
//!
//! This module provides comprehensive model loading, configuration, and management
//! capabilities for the Qilin inference engine.

pub mod config;
pub mod loader;
pub mod registry;
pub mod mapping;
pub mod serialization;

#[cfg(test)]
mod tests;

// Re-export commonly used types
pub use config::{ModelConfig, HuggingFaceConfig};
pub use loader::{ModelLoader, LoaderConfig};
pub use registry::{Model, ModelFactory, ModelRegistry, ModelMetadata, FactoryInfo};
pub use mapping::{WeightMapper, WeightMapping, WeightTransform, MappingStats};
pub use serialization::{ModelSerializer, SerializationConfig, SerializationFormat, CompressionType, CheckpointMetadata};