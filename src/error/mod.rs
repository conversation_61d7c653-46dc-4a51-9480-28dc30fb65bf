//! Error handling for the inference engine.
//!
//! This module provides a comprehensive error handling system with:
//! - Hierarchical error types
//! - Rich error context
//! - Error recovery strategies
//! - Performance monitoring

use std::collections::HashMap;
use std::fmt;
use std::path::PathBuf;
use std::time::{Duration, SystemTime};
use thiserror::Error;

pub mod recovery;
pub mod monitor;

/// Core trait for all inference engine errors.
pub trait InferenceError: std::error::Error + Send + Sync + 'static {
    /// Get the error code for categorization.
    fn error_code(&self) -> ErrorCode;
    
    /// Get the severity level of this error.
    fn severity(&self) -> ErrorSeverity;
    
    /// Get additional context information.
    fn context(&self) -> Option<&ErrorContext>;
    
    /// Check if this error is recoverable.
    fn recoverable(&self) -> bool;
}

/// Error codes for categorization and monitoring.
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>q, <PERSON>h)]
#[repr(u32)]
pub enum ErrorCode {
    // Tensor errors (1000-1999)
    TensorShapeMismatch = 1001,
    TensorInvalidDimension = 1002,
    TensorMemoryAllocation = 1003,
    TensorDeviceMismatch = 1004,
    TensorDataTypeIncompatible = 1005,
    TensorIndexOutOfBounds = 1006,
    
    // Model errors (2000-2999)
    ModelConfigurationInvalid = 2001,
    ModelWeightLoadingFailed = 2002,
    ModelArchitectureUnsupported = 2003,
    ModelParameterMissing = 2004,
    ModelVersionIncompatible = 2005,
    
    // Inference errors (3000-3999)
    InferenceInputInvalid = 3001,
    InferenceMemoryExhausted = 3002,
    InferenceTimeoutExceeded = 3003,
    InferenceDeviceUnavailable = 3004,
    InferenceBatchSizeExceeded = 3005,
    
    // Attention errors (5000-5999)
    AttentionConfigurationInvalid = 5001,
    AttentionDimensionMismatch = 5002,
    AttentionMaskInvalid = 5003,
    AttentionCacheOverflow = 5004,
    AttentionNumericalInstability = 5005,
    AttentionHeadCountInvalid = 5006,

    // Cache errors (6000-6999)
    CacheCapacityExceeded = 6001,
    CacheKeyNotFound = 6002,
    CacheInvalidState = 6003,
    CacheMemoryExhausted = 6004,
    CacheCorrupted = 6005,
    CacheVersionMismatch = 6006,

    // System errors (4000-4999)
    SystemResourceExhausted = 4001,
    SystemDeviceError = 4002,
    SystemConcurrencyError = 4003,
    SystemConfigurationError = 4004,
    SystemIOError = 4005,
}

/// Error severity levels.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    /// Informational - operation can continue normally.
    Info,
    /// Warning - operation can continue but with degraded performance.
    Warning,
    /// Error - operation failed but system can recover.
    Error,
    /// Critical - serious error that may affect system stability.
    Critical,
    /// Fatal - unrecoverable error, system must shut down.
    Fatal,
}

/// Rich error context information.
#[derive(Debug, Clone)]
pub struct ErrorContext {
    /// The operation that was being performed.
    pub operation: String,
    /// The module where the error occurred.
    pub module: String,
    /// Source file name.
    pub file: String,
    /// Line number in source file.
    pub line: u32,
    /// Thread ID where error occurred.
    pub thread_id: std::thread::ThreadId,
    /// Timestamp when error occurred.
    pub timestamp: SystemTime,
    /// Additional context information.
    pub additional_info: HashMap<String, String>,
}

impl ErrorContext {
    /// Create a new error context.
    pub fn new(operation: &str, module: &str) -> Self {
        Self {
            operation: operation.to_string(),
            module: module.to_string(),
            file: file!().to_string(),
            line: line!(),
            thread_id: std::thread::current().id(),
            timestamp: SystemTime::now(),
            additional_info: HashMap::new(),
        }
    }
    
    /// Add additional context information.
    pub fn with_info<K: Into<String>, V: Into<String>>(mut self, key: K, value: V) -> Self {
        self.additional_info.insert(key.into(), value.into());
        self
    }
}

/// Tensor-related errors.
#[derive(Debug, Error)]
pub enum TensorError {
    #[error("Shape mismatch: expected {expected:?}, got {actual:?}")]
    ShapeMismatch {
        expected: Vec<usize>,
        actual: Vec<usize>,
        context: Option<ErrorContext>,
    },
    
    #[error("Invalid dimension: {dimension} for tensor with {total_dims} dimensions")]
    InvalidDimension {
        dimension: usize,
        total_dims: usize,
        context: Option<ErrorContext>,
    },
    
    #[error("Memory allocation failed: requested {requested_bytes} bytes")]
    MemoryAllocation {
        requested_bytes: usize,
        available_bytes: Option<usize>,
        context: Option<ErrorContext>,
    },
    
    #[error("Device mismatch: tensor on {tensor_device}, operation requires {required_device}")]
    DeviceMismatch {
        tensor_device: String,
        required_device: String,
        context: Option<ErrorContext>,
    },
    
    #[error("Data type incompatible: {operation} not supported for {dtype}")]
    DataTypeIncompatible {
        operation: String,
        dtype: String,
        context: Option<ErrorContext>,
    },
    
    #[error("Index out of bounds: index {index} >= size {size}")]
    IndexOutOfBounds {
        index: usize,
        size: usize,
        context: Option<ErrorContext>,
    },

    #[error("Computation error: {message}")]
    ComputationError {
        message: String,
        context: Option<ErrorContext>,
    },

    #[error("Unsupported operation: {operation}")]
    UnsupportedOperation {
        operation: String,
        context: Option<ErrorContext>,
    },
}

impl InferenceError for TensorError {
    fn error_code(&self) -> ErrorCode {
        match self {
            TensorError::ShapeMismatch { .. } => ErrorCode::TensorShapeMismatch,
            TensorError::InvalidDimension { .. } => ErrorCode::TensorInvalidDimension,
            TensorError::MemoryAllocation { .. } => ErrorCode::TensorMemoryAllocation,
            TensorError::DeviceMismatch { .. } => ErrorCode::TensorDeviceMismatch,
            TensorError::DataTypeIncompatible { .. } => ErrorCode::TensorDataTypeIncompatible,
            TensorError::IndexOutOfBounds { .. } => ErrorCode::TensorIndexOutOfBounds,
            TensorError::ComputationError { .. } => ErrorCode::TensorShapeMismatch, // Use existing code
            TensorError::UnsupportedOperation { .. } => ErrorCode::TensorDataTypeIncompatible, // Use existing code
        }
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            TensorError::ShapeMismatch { .. } => ErrorSeverity::Error,
            TensorError::InvalidDimension { .. } => ErrorSeverity::Error,
            TensorError::MemoryAllocation { .. } => ErrorSeverity::Critical,
            TensorError::DeviceMismatch { .. } => ErrorSeverity::Error,
            TensorError::DataTypeIncompatible { .. } => ErrorSeverity::Error,
            TensorError::IndexOutOfBounds { .. } => ErrorSeverity::Error,
            TensorError::ComputationError { .. } => ErrorSeverity::Error,
            TensorError::UnsupportedOperation { .. } => ErrorSeverity::Error,
        }
    }
    
    fn context(&self) -> Option<&ErrorContext> {
        match self {
            TensorError::ShapeMismatch { context, .. } => context.as_ref(),
            TensorError::InvalidDimension { context, .. } => context.as_ref(),
            TensorError::MemoryAllocation { context, .. } => context.as_ref(),
            TensorError::DeviceMismatch { context, .. } => context.as_ref(),
            TensorError::DataTypeIncompatible { context, .. } => context.as_ref(),
            TensorError::IndexOutOfBounds { context, .. } => context.as_ref(),
            TensorError::ComputationError { context, .. } => context.as_ref(),
            TensorError::UnsupportedOperation { context, .. } => context.as_ref(),
        }
    }
    
    fn recoverable(&self) -> bool {
        match self {
            TensorError::MemoryAllocation { .. } => false,
            _ => true,
        }
    }
}

/// Model-related errors.
#[derive(Debug, Error)]
pub enum ModelError {
    #[error("Invalid model configuration: {message}")]
    InvalidConfiguration {
        message: String,
        config_field: Option<String>,
        context: Option<ErrorContext>,
    },
    
    #[error("Weight loading failed: {path}")]
    WeightLoadingFailed {
        path: PathBuf,
        io_error: Option<std::io::Error>,
        context: Option<ErrorContext>,
    },
    
    #[error("Unsupported architecture: {architecture}")]
    UnsupportedArchitecture {
        architecture: String,
        supported_architectures: Vec<String>,
        context: Option<ErrorContext>,
    },
    
    #[error("Missing parameter: {parameter_name}")]
    ParameterMissing {
        parameter_name: String,
        context: Option<ErrorContext>,
    },
    
    #[error("Tensor error: {0}")]
    Tensor(#[from] TensorError),
}

/// Attention-related errors.
#[derive(Debug, Error)]
pub enum AttentionError {
    #[error("Invalid attention configuration: {message}")]
    InvalidConfiguration {
        message: String,
        config_field: Option<String>,
        context: Option<ErrorContext>,
    },

    #[error("Attention dimension mismatch: expected {expected}, got {actual}")]
    DimensionMismatch {
        expected: String,
        actual: String,
        operation: String,
        context: Option<ErrorContext>,
    },

    #[error("Invalid attention mask: {reason}")]
    InvalidMask {
        reason: String,
        mask_shape: Option<Vec<usize>>,
        expected_shape: Option<Vec<usize>>,
        context: Option<ErrorContext>,
    },

    #[error("Attention cache overflow: current size {current_size}, max size {max_size}")]
    CacheOverflow {
        current_size: usize,
        max_size: usize,
        cache_type: String,
        context: Option<ErrorContext>,
    },

    #[error("Numerical instability in attention: {operation}")]
    NumericalInstability {
        operation: String,
        details: String,
        context: Option<ErrorContext>,
    },

    #[error("Invalid head count: {head_count} heads, model dimension {model_dim}")]
    InvalidHeadCount {
        head_count: usize,
        model_dim: usize,
        context: Option<ErrorContext>,
    },

    #[error("Tensor error: {0}")]
    Tensor(#[from] TensorError),

    #[error("Cache error: {0}")]
    Cache(#[from] CacheError),

    #[error("Model error: {0}")]
    Model(#[from] ModelError),
}

impl InferenceError for AttentionError {
    fn error_code(&self) -> ErrorCode {
        match self {
            AttentionError::InvalidConfiguration { .. } => ErrorCode::AttentionConfigurationInvalid,
            AttentionError::DimensionMismatch { .. } => ErrorCode::AttentionDimensionMismatch,
            AttentionError::InvalidMask { .. } => ErrorCode::AttentionMaskInvalid,
            AttentionError::CacheOverflow { .. } => ErrorCode::AttentionCacheOverflow,
            AttentionError::NumericalInstability { .. } => ErrorCode::AttentionNumericalInstability,
            AttentionError::InvalidHeadCount { .. } => ErrorCode::AttentionHeadCountInvalid,
            AttentionError::Tensor(tensor_error) => tensor_error.error_code(),
            AttentionError::Cache(cache_error) => cache_error.error_code(),
            AttentionError::Model(model_error) => model_error.error_code(),
        }
    }

    fn severity(&self) -> ErrorSeverity {
        match self {
            AttentionError::InvalidConfiguration { .. } => ErrorSeverity::Error,
            AttentionError::DimensionMismatch { .. } => ErrorSeverity::Error,
            AttentionError::InvalidMask { .. } => ErrorSeverity::Warning,
            AttentionError::CacheOverflow { .. } => ErrorSeverity::Critical,
            AttentionError::NumericalInstability { .. } => ErrorSeverity::Warning,
            AttentionError::InvalidHeadCount { .. } => ErrorSeverity::Error,
            AttentionError::Tensor(tensor_error) => tensor_error.severity(),
            AttentionError::Cache(cache_error) => cache_error.severity(),
            AttentionError::Model(model_error) => model_error.severity(),
        }
    }

    fn context(&self) -> Option<&ErrorContext> {
        match self {
            AttentionError::InvalidConfiguration { context, .. } => context.as_ref(),
            AttentionError::DimensionMismatch { context, .. } => context.as_ref(),
            AttentionError::InvalidMask { context, .. } => context.as_ref(),
            AttentionError::CacheOverflow { context, .. } => context.as_ref(),
            AttentionError::NumericalInstability { context, .. } => context.as_ref(),
            AttentionError::InvalidHeadCount { context, .. } => context.as_ref(),
            AttentionError::Tensor(tensor_error) => tensor_error.context(),
            AttentionError::Cache(cache_error) => cache_error.context(),
            AttentionError::Model(model_error) => model_error.context(),
        }
    }

    fn recoverable(&self) -> bool {
        match self {
            AttentionError::CacheOverflow { .. } => false,
            AttentionError::NumericalInstability { .. } => true,
            AttentionError::InvalidMask { .. } => true,
            AttentionError::Tensor(tensor_error) => tensor_error.recoverable(),
            AttentionError::Cache(cache_error) => cache_error.recoverable(),
            AttentionError::Model(model_error) => model_error.recoverable(),
            _ => false,
        }
    }
}

/// Cache-related errors.
#[derive(Debug, Error)]
pub enum CacheError {
    #[error("Cache capacity exceeded: current size {current_size}, max capacity {max_capacity}")]
    CapacityExceeded {
        current_size: usize,
        max_capacity: usize,
        cache_type: String,
        context: Option<ErrorContext>,
    },

    #[error("Cache key not found: {key}")]
    KeyNotFound {
        key: String,
        cache_type: String,
        context: Option<ErrorContext>,
    },

    #[error("Cache in invalid state: {reason}")]
    InvalidState {
        reason: String,
        current_state: String,
        expected_state: String,
        context: Option<ErrorContext>,
    },

    #[error("Cache memory exhausted: requested {requested_bytes} bytes, available {available_bytes} bytes")]
    MemoryExhausted {
        requested_bytes: usize,
        available_bytes: usize,
        context: Option<ErrorContext>,
    },

    #[error("Cache corrupted: {reason}")]
    Corrupted {
        reason: String,
        corruption_type: String,
        context: Option<ErrorContext>,
    },

    #[error("Cache version mismatch: expected {expected_version}, found {actual_version}")]
    VersionMismatch {
        expected_version: String,
        actual_version: String,
        context: Option<ErrorContext>,
    },

    #[error("Tensor error: {0}")]
    Tensor(#[from] TensorError),
}

impl InferenceError for CacheError {
    fn error_code(&self) -> ErrorCode {
        match self {
            CacheError::CapacityExceeded { .. } => ErrorCode::CacheCapacityExceeded,
            CacheError::KeyNotFound { .. } => ErrorCode::CacheKeyNotFound,
            CacheError::InvalidState { .. } => ErrorCode::CacheInvalidState,
            CacheError::MemoryExhausted { .. } => ErrorCode::CacheMemoryExhausted,
            CacheError::Corrupted { .. } => ErrorCode::CacheCorrupted,
            CacheError::VersionMismatch { .. } => ErrorCode::CacheVersionMismatch,
            CacheError::Tensor(tensor_error) => tensor_error.error_code(),
        }
    }

    fn severity(&self) -> ErrorSeverity {
        match self {
            CacheError::CapacityExceeded { .. } => ErrorSeverity::Warning,
            CacheError::KeyNotFound { .. } => ErrorSeverity::Warning,
            CacheError::InvalidState { .. } => ErrorSeverity::Error,
            CacheError::MemoryExhausted { .. } => ErrorSeverity::Critical,
            CacheError::Corrupted { .. } => ErrorSeverity::Critical,
            CacheError::VersionMismatch { .. } => ErrorSeverity::Error,
            CacheError::Tensor(tensor_error) => tensor_error.severity(),
        }
    }

    fn context(&self) -> Option<&ErrorContext> {
        match self {
            CacheError::CapacityExceeded { context, .. } => context.as_ref(),
            CacheError::KeyNotFound { context, .. } => context.as_ref(),
            CacheError::InvalidState { context, .. } => context.as_ref(),
            CacheError::MemoryExhausted { context, .. } => context.as_ref(),
            CacheError::Corrupted { context, .. } => context.as_ref(),
            CacheError::VersionMismatch { context, .. } => context.as_ref(),
            CacheError::Tensor(tensor_error) => tensor_error.context(),
        }
    }

    fn recoverable(&self) -> bool {
        match self {
            CacheError::CapacityExceeded { .. } => true,  // Can clear cache
            CacheError::KeyNotFound { .. } => true,       // Can regenerate
            CacheError::InvalidState { .. } => false,     // Requires reset
            CacheError::MemoryExhausted { .. } => true,   // Can free memory
            CacheError::Corrupted { .. } => false,        // Requires rebuild
            CacheError::VersionMismatch { .. } => false,  // Requires migration
            CacheError::Tensor(tensor_error) => tensor_error.recoverable(),
        }
    }
}

impl InferenceError for ModelError {
    fn error_code(&self) -> ErrorCode {
        match self {
            ModelError::InvalidConfiguration { .. } => ErrorCode::ModelConfigurationInvalid,
            ModelError::WeightLoadingFailed { .. } => ErrorCode::ModelWeightLoadingFailed,
            ModelError::UnsupportedArchitecture { .. } => ErrorCode::ModelArchitectureUnsupported,
            ModelError::ParameterMissing { .. } => ErrorCode::ModelParameterMissing,
            ModelError::Tensor(tensor_error) => tensor_error.error_code(),
        }
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            ModelError::InvalidConfiguration { .. } => ErrorSeverity::Error,
            ModelError::WeightLoadingFailed { .. } => ErrorSeverity::Critical,
            ModelError::UnsupportedArchitecture { .. } => ErrorSeverity::Error,
            ModelError::ParameterMissing { .. } => ErrorSeverity::Error,
            ModelError::Tensor(tensor_error) => tensor_error.severity(),
        }
    }
    
    fn context(&self) -> Option<&ErrorContext> {
        match self {
            ModelError::InvalidConfiguration { context, .. } => context.as_ref(),
            ModelError::WeightLoadingFailed { context, .. } => context.as_ref(),
            ModelError::UnsupportedArchitecture { context, .. } => context.as_ref(),
            ModelError::ParameterMissing { context, .. } => context.as_ref(),
            ModelError::Tensor(tensor_error) => tensor_error.context(),
        }
    }
    
    fn recoverable(&self) -> bool {
        match self {
            ModelError::WeightLoadingFailed { .. } => false,
            ModelError::Tensor(tensor_error) => tensor_error.recoverable(),
            _ => true,
        }
    }
}

/// Convenience macro for creating error context.
#[macro_export]
macro_rules! error_context {
    ($operation:expr, $module:expr) => {
        $crate::error::ErrorContext::new($operation, $module)
    };
    ($operation:expr, $module:expr, $($key:expr => $value:expr),+) => {
        {
            let mut ctx = $crate::error::ErrorContext::new($operation, $module);
            $(
                ctx = ctx.with_info($key, $value);
            )+
            ctx
        }
    };
}

/// Result type alias for inference operations.
pub type Result<T> = std::result::Result<T, Box<dyn InferenceError>>;

// Conversion implementations
impl From<AttentionError> for TensorError {
    fn from(err: AttentionError) -> Self {
        TensorError::ComputationError {
            message: format!("Attention error: {}", err),
            context: err.context().cloned(),
        }
    }
}
