//! Transformer decoder implementation.
//!
//! This module provides the TransformerDecoderLayer and TransformerDecoder
//! implementations following the standard Transformer architecture.

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, Numeric, Shape, cpu::{C<PERSON>Tensor, CpuTensorFactory}};
use crate::layers::{
    Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit,
    feedforward::{FeedForward, FeedForwardConfig},
    norm::{LayerNorm, LayerNormConfig, RMSNorm, RMSNormConfig},
    dropout::{Dropout, DropoutConfig},
    activation::ActivationType as LayerActivationType,
};
use crate::attention::{MultiHeadAttention, AttentionConfig};
use crate::error::{TensorError, ErrorContext};
use super::config::{TransformerConfig, ActivationType, NormalizationType, ModelType};
use super::encoder::{NormalizationLayer, create_normalization_layer, convert_activation_type, EncoderLayerConfig};

/// Configuration for a single Transformer decoder layer.
#[derive(Debug, Clone)]
pub struct DecoderLayerConfig {
    /// Hidden dimension size
    pub hidden_size: usize,
    /// Number of attention heads
    pub num_attention_heads: usize,
    /// Intermediate (FFN) dimension size
    pub intermediate_size: usize,
    /// Dropout probability
    pub dropout: f32,
    /// Attention dropout probability
    pub attention_dropout: f32,
    /// Cross-attention dropout probability (for encoder-decoder models)
    pub cross_attention_dropout: Option<f32>,
    /// Activation function type
    pub activation_function: ActivationType,
    /// Normalization type
    pub normalization_type: NormalizationType,
    /// Whether to use pre-normalization
    pub pre_norm: bool,
    /// Layer normalization epsilon
    pub layer_norm_eps: f32,
    /// Whether to use bias in attention
    pub attention_bias: bool,
    /// Whether to use bias in linear layers
    pub use_bias: bool,
    /// Whether to use gated FFN
    pub use_gated_ffn: bool,
    /// Whether this is a decoder-only model (no cross-attention)
    pub decoder_only: bool,
}

impl From<&TransformerConfig> for DecoderLayerConfig {
    fn from(config: &TransformerConfig) -> Self {
        Self {
            hidden_size: config.hidden_size,
            num_attention_heads: config.num_attention_heads,
            intermediate_size: config.intermediate_size,
            dropout: config.dropout,
            attention_dropout: config.attention_dropout,
            cross_attention_dropout: config.cross_attention_dropout,
            activation_function: config.activation_function,
            normalization_type: config.normalization_type,
            pre_norm: config.pre_norm,
            layer_norm_eps: config.layer_norm_eps,
            attention_bias: config.attention_bias,
            use_bias: config.use_bias,
            use_gated_ffn: config.use_gated_ffn,
            decoder_only: config.model_type == ModelType::DecoderOnly,
        }
    }
}

impl From<&DecoderLayerConfig> for EncoderLayerConfig {
    fn from(config: &DecoderLayerConfig) -> Self {
        Self {
            hidden_size: config.hidden_size,
            num_attention_heads: config.num_attention_heads,
            intermediate_size: config.intermediate_size,
            dropout: config.dropout,
            attention_dropout: config.attention_dropout,
            activation_function: config.activation_function,
            normalization_type: config.normalization_type,
            pre_norm: config.pre_norm,
            layer_norm_eps: config.layer_norm_eps,
            attention_bias: config.attention_bias,
            use_bias: config.use_bias,
            use_gated_ffn: config.use_gated_ffn,
        }
    }
}

/// Single Transformer decoder layer.
///
/// Implements the standard Transformer decoder layer with:
/// - Masked multi-head self-attention
/// - Optional cross-attention (for encoder-decoder models)
/// - Position-wise feed-forward network
/// - Residual connections around each sub-layer
/// - Layer normalization (pre-norm or post-norm)
/// - Dropout for regularization
///
/// # Examples
///
/// ```rust
/// use qilin_inference::transformer::{TransformerDecoderLayer, DecoderLayerConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
/// use qilin_inference::layers::ParameterInit;
///
/// // Create decoder layer configuration
/// let config = DecoderLayerConfig {
///     hidden_size: 512,
///     num_attention_heads: 8,
///     intermediate_size: 2048,
///     dropout: 0.1,
///     attention_dropout: 0.1,
///     decoder_only: true, // GPT-style decoder-only
///     // ... other config fields
/// };
///
/// // Create and initialize decoder layer
/// let mut layer = TransformerDecoderLayer::new(config).unwrap();
/// layer.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass (decoder-only)
/// let input = CpuTensor::randn(&Shape::new(vec![2, 10, 512]), 0.0, 1.0).unwrap();
/// let output = layer.forward(&input, None, None, None).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 10, 512]);
/// ```
#[derive(Debug)]
pub struct TransformerDecoderLayer<T: Numeric> {
    /// Masked multi-head self-attention layer
    self_attention: MultiHeadAttention<T>,
    /// Optional cross-attention layer (for encoder-decoder models)
    cross_attention: Option<MultiHeadAttention<T>>,
    /// Feed-forward network
    feed_forward: FeedForward<T>,
    /// First normalization layer (for self-attention)
    norm1: Box<dyn NormalizationLayer<T>>,
    /// Second normalization layer (for cross-attention, if present)
    norm2: Option<Box<dyn NormalizationLayer<T>>>,
    /// Third normalization layer (for FFN)
    norm3: Box<dyn NormalizationLayer<T>>,
    /// Dropout layer
    dropout: Option<Dropout<T>>,
    /// Layer configuration
    config: DecoderLayerConfig,
    /// Training mode flag
    training: bool,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> TransformerDecoderLayer<T> {
    /// Create a new Transformer decoder layer.
    pub fn new(config: DecoderLayerConfig) -> Result<Self, TensorError> {
        // Create self-attention layer
        let self_attention_config = AttentionConfig::new(config.hidden_size, config.num_attention_heads)
            .with_dropout(config.attention_dropout)
            .with_bias(config.attention_bias);
        let self_attention = MultiHeadAttention::new(self_attention_config)?;
        
        // Create cross-attention layer if not decoder-only
        let cross_attention = if !config.decoder_only {
            let cross_attention_dropout = config.cross_attention_dropout.unwrap_or(config.attention_dropout);
            let cross_attention_config = AttentionConfig::new(config.hidden_size, config.num_attention_heads)
                .with_dropout(cross_attention_dropout)
                .with_bias(config.attention_bias);
            Some(MultiHeadAttention::new(cross_attention_config)?)
        } else {
            None
        };
        
        // Create feed-forward network
        let ff_config = FeedForwardConfig::new(config.hidden_size, config.intermediate_size)
            .with_activation(convert_activation_type(config.activation_function))
            .with_bias(config.use_bias)
            .with_gated(config.use_gated_ffn);
        let feed_forward = FeedForward::new(ff_config)?;
        
        // Create normalization layers
        let encoder_config: EncoderLayerConfig = (&config).into();
        let norm1 = create_normalization_layer(&encoder_config)?;
        let norm2 = if cross_attention.is_some() {
            Some(create_normalization_layer(&encoder_config)?)
        } else {
            None
        };
        let norm3 = create_normalization_layer(&encoder_config)?;
        
        // Create dropout layer if needed
        let dropout = if config.dropout > 0.0 {
            let dropout_config = DropoutConfig::new(config.dropout);
            Some(Dropout::new(dropout_config)?)
        } else {
            None
        };
        
        Ok(Self {
            self_attention,
            cross_attention,
            feed_forward,
            norm1,
            norm2,
            norm3,
            dropout,
            config,
            training: true,
            _phantom: PhantomData,
        })
    }
    
    /// Initialize all parameters in the layer.
    pub fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        // Initialize self-attention parameters
        self.self_attention.init_parameters(init_strategy.clone())?;

        // Initialize cross-attention parameters if present
        if let Some(ref mut cross_attention) = self.cross_attention {
            cross_attention.init_parameters(init_strategy.clone())?;
        }

        // Initialize feed-forward parameters
        self.feed_forward.init_parameters(init_strategy.clone())?;

        // Initialize normalization parameters
        self.norm1.init_parameters(init_strategy.clone())?;
        if let Some(ref mut norm2) = self.norm2 {
            norm2.init_parameters(init_strategy.clone())?;
        }
        self.norm3.init_parameters(init_strategy)?;
        
        Ok(())
    }
    
    /// Set training mode for all sub-layers.
    pub fn set_training(&mut self, training: bool) {
        self.training = training;
        self.self_attention.set_training(training);
        if let Some(ref mut cross_attention) = self.cross_attention {
            cross_attention.set_training(training);
        }
        self.feed_forward.set_training(training);
        self.norm1.set_training(training);
        if let Some(ref mut norm2) = self.norm2 {
            norm2.set_training(training);
        }
        self.norm3.set_training(training);
        if let Some(ref mut dropout) = self.dropout {
            dropout.set_training(training);
        }
    }
    
    /// Get the layer configuration.
    pub fn config(&self) -> &DecoderLayerConfig {
        &self.config
    }
    
    /// Check if this layer has cross-attention.
    pub fn has_cross_attention(&self) -> bool {
        self.cross_attention.is_some()
    }

    /// Forward pass through the decoder layer.
    ///
    /// # Arguments
    /// * `input` - Input tensor with shape [batch_size, seq_len, hidden_size]
    /// * `encoder_output` - Optional encoder output for cross-attention
    /// * `self_attention_mask` - Optional mask for self-attention (causal mask for autoregressive models)
    /// * `cross_attention_mask` - Optional mask for cross-attention
    ///
    /// # Returns
    /// Output tensor with the same shape as input
    pub fn forward(
        &self,
        input: &CpuTensor<T>,
        encoder_output: Option<&CpuTensor<T>>,
        self_attention_mask: Option<&CpuTensor<T>>,
        cross_attention_mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        // Validate input shape
        if input.shape().dims().len() != 3 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, 0, self.config.hidden_size], // batch, seq_len, hidden_size
                actual: input.shape().dims().to_vec(),
                context: Some(ErrorContext::new("forward", "decoder.rs")),
            });
        }

        if input.shape().dims()[2] != self.config.hidden_size {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, 0, self.config.hidden_size],
                actual: input.shape().dims().to_vec(),
                context: Some(ErrorContext::new("forward", "decoder.rs")),
            });
        }

        let mut x = input.clone();

        // 1. Masked self-attention sub-layer
        let residual1 = x.clone();

        // Pre-normalization
        if self.config.pre_norm {
            x = self.norm1.forward(&x)?;
        }

        // Masked self-attention
        let (self_attention_output, _self_attention_weights) =
            self.self_attention.forward(&x, &x, &x, self_attention_mask)?;

        // Apply dropout if configured
        let self_attention_output = if let Some(ref dropout) = self.dropout {
            dropout.forward(self_attention_output)?
        } else {
            self_attention_output
        };

        // Residual connection
        x = residual1.add(&self_attention_output)?;

        // Post-normalization
        if !self.config.pre_norm {
            x = self.norm1.forward(&x)?;
        }

        // 2. Cross-attention sub-layer (if present)
        if let (Some(ref cross_attention), Some(ref norm2), Some(encoder_out)) =
            (&self.cross_attention, &self.norm2, encoder_output) {

            let residual2 = x.clone();

            // Pre-normalization
            if self.config.pre_norm {
                x = norm2.forward(&x)?;
            }

            // Cross-attention: query from decoder, key and value from encoder
            let (cross_attention_output, _cross_attention_weights) =
                cross_attention.forward(&x, encoder_out, encoder_out, cross_attention_mask)?;

            // Apply dropout if configured
            let cross_attention_output = if let Some(ref dropout) = self.dropout {
                dropout.forward(cross_attention_output)?
            } else {
                cross_attention_output
            };

            // Residual connection
            x = residual2.add(&cross_attention_output)?;

            // Post-normalization
            if !self.config.pre_norm {
                x = norm2.forward(&x)?;
            }
        }

        // 3. Feed-forward sub-layer
        let residual3 = x.clone();

        // Pre-normalization
        if self.config.pre_norm {
            x = self.norm3.forward(&x)?;
        }

        // Feed-forward network
        let ff_output = self.feed_forward.forward(x)?;

        // Apply dropout if configured
        let ff_output = if let Some(ref dropout) = self.dropout {
            dropout.forward(ff_output)?
        } else {
            ff_output
        };

        // Residual connection
        x = residual3.add(&ff_output)?;

        // Post-normalization
        if !self.config.pre_norm {
            x = self.norm3.forward(&x)?;
        }

        Ok(x)
    }
}

/// Multi-layer Transformer decoder.
///
/// Stacks multiple TransformerDecoderLayer instances to form a complete decoder.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::transformer::{TransformerDecoder, TransformerConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
/// use qilin_inference::layers::ParameterInit;
///
/// // Create decoder from config
/// let config = TransformerConfig::gpt2_small();
/// let mut decoder = TransformerDecoder::new(&config).unwrap();
/// decoder.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass (decoder-only)
/// let input = CpuTensor::randn(&Shape::new(vec![2, 128, 768]), 0.0, 1.0).unwrap();
/// let output = decoder.forward(&input, None, None, None).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 128, 768]);
/// ```
#[derive(Debug)]
pub struct TransformerDecoder<T: Numeric> {
    /// Stack of decoder layers
    layers: Vec<TransformerDecoderLayer<T>>,
    /// Optional final normalization layer
    final_norm: Option<Box<dyn NormalizationLayer<T>>>,
    /// Number of layers
    num_layers: usize,
    /// Whether this is a decoder-only model
    decoder_only: bool,
    /// Training mode flag
    training: bool,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> TransformerDecoder<T> {
    /// Create a new Transformer decoder from configuration.
    pub fn new(config: &TransformerConfig) -> Result<Self, TensorError> {
        let layer_config = DecoderLayerConfig::from(config);
        let num_layers = match config.model_type {
            ModelType::DecoderOnly => config.num_layers,
            ModelType::EncoderDecoder => config.num_decoder_layers.unwrap_or(config.num_layers / 2),
            _ => return Err(TensorError::UnsupportedOperation {
                operation: format!("Model type {:?} for decoder", config.model_type),
                context: Some(ErrorContext::new("new", "decoder.rs")),
            }),
        };

        let mut layers = Vec::with_capacity(num_layers);

        // Create decoder layers
        for _ in 0..num_layers {
            let layer = TransformerDecoderLayer::new(layer_config.clone())?;
            layers.push(layer);
        }

        // Create final normalization layer if using pre-norm
        let final_norm = if config.pre_norm {
            let encoder_config: EncoderLayerConfig = (&layer_config).into();
            Some(create_normalization_layer(&encoder_config)?)
        } else {
            None
        };

        Ok(Self {
            layers,
            final_norm,
            num_layers,
            decoder_only: layer_config.decoder_only,
            training: true,
            _phantom: PhantomData,
        })
    }

    /// Initialize parameters for all layers.
    pub fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        for layer in &mut self.layers {
            layer.init_parameters(init_strategy.clone())?;
        }

        if let Some(ref mut final_norm) = self.final_norm {
            final_norm.init_parameters(init_strategy)?;
        }

        Ok(())
    }

    /// Set training mode for all layers.
    pub fn set_training(&mut self, training: bool) {
        self.training = training;
        for layer in &mut self.layers {
            layer.set_training(training);
        }

        if let Some(ref mut final_norm) = self.final_norm {
            final_norm.set_training(training);
        }
    }

    /// Get the number of layers.
    pub fn num_layers(&self) -> usize {
        self.num_layers
    }

    /// Check if this is a decoder-only model.
    pub fn is_decoder_only(&self) -> bool {
        self.decoder_only
    }

    /// Forward pass through all decoder layers.
    ///
    /// # Arguments
    /// * `input` - Input tensor with shape [batch_size, seq_len, hidden_size]
    /// * `encoder_output` - Optional encoder output for cross-attention
    /// * `self_attention_mask` - Optional mask for self-attention
    /// * `cross_attention_mask` - Optional mask for cross-attention
    ///
    /// # Returns
    /// Output tensor with the same shape as input
    pub fn forward(
        &self,
        input: &CpuTensor<T>,
        encoder_output: Option<&CpuTensor<T>>,
        self_attention_mask: Option<&CpuTensor<T>>,
        cross_attention_mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        let mut x = input.clone();

        // Pass through each decoder layer
        for layer in &self.layers {
            x = layer.forward(&x, encoder_output, self_attention_mask, cross_attention_mask)?;
        }

        // Apply final normalization if present
        if let Some(ref final_norm) = self.final_norm {
            x = final_norm.forward(&x)?;
        }

        Ok(x)
    }
}
