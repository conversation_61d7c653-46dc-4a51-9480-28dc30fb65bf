//! Transformer configuration system.
//!
//! This module provides comprehensive configuration management for Transformer models,
//! supporting various architectures and optimization strategies.

use serde::{Deserialize, Serialize};

/// Activation function types supported by Transformer models.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum ActivationType {
    /// Rectified Linear Unit
    ReLU,
    /// Gaussian Error Linear Unit
    GELU,
    /// Swish activation (SiLU)
    Swish,
    /// Swish-Gated Linear Unit
    SwiGLU,
    /// GELU-Gated Linear Unit
    GeGLU,
    /// Hyperbolic tangent
    Tanh,
}

impl Default for ActivationType {
    fn default() -> Self {
        Self::GELU
    }
}

/// Normalization layer types.
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum NormalizationType {
    /// Layer Normalization
    LayerNorm,
    /// Root Mean Square Normalization
    RMSNorm,
    /// Batch Normalization
    BatchNorm,
    /// Group Normalization
    GroupNorm,
}

impl Default for NormalizationType {
    fn default() -> Self {
        Self::LayerNorm
    }
}

/// Position encoding types.
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum PositionEncodingType {
    /// Learnable absolute position embeddings
    Learned,
    /// Fixed sinusoidal position embeddings
    Sinusoidal,
    /// Rotary Position Embedding (RoPE)
    RoPE,
    /// Attention with Linear Biases (ALiBi)
    ALiBi,
    /// T5-style relative position bias
    RelativeBias,
    /// No position encoding
    None,
}

impl Default for PositionEncodingType {
    fn default() -> Self {
        Self::Learned
    }
}

/// Transformer model architecture types.
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ModelType {
    /// Encoder-only model (like BERT)
    EncoderOnly,
    /// Decoder-only model (like GPT)
    DecoderOnly,
    /// Encoder-decoder model (like T5)
    EncoderDecoder,
}

impl Default for ModelType {
    fn default() -> Self {
        Self::DecoderOnly
    }
}

/// Comprehensive Transformer configuration.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransformerConfig {
    // Model architecture
    /// Model type (encoder-only, decoder-only, encoder-decoder)
    pub model_type: ModelType,
    /// Size of the vocabulary
    pub vocab_size: usize,
    /// Hidden dimension size
    pub hidden_size: usize,
    /// Number of transformer layers
    pub num_layers: usize,
    /// Number of attention heads
    pub num_attention_heads: usize,
    /// Size of the intermediate (feed-forward) layer
    pub intermediate_size: usize,
    /// Maximum position embeddings
    pub max_position_embeddings: usize,
    
    // Attention configuration
    /// Attention dropout probability
    pub attention_dropout: f32,
    /// Whether to use bias in attention projections
    pub attention_bias: bool,
    /// Custom attention scale factor (if None, uses 1/sqrt(head_dim))
    pub attention_scale: Option<f32>,
    
    // Layer configuration
    /// General dropout probability
    pub dropout: f32,
    /// Activation function type
    pub activation_function: ActivationType,
    /// Normalization type
    pub normalization_type: NormalizationType,
    /// Position encoding type
    pub position_encoding_type: PositionEncodingType,
    /// Whether to use pre-normalization (True) or post-normalization (False)
    pub pre_norm: bool,
    /// Epsilon for normalization layers
    pub layer_norm_eps: f32,
    
    // Advanced features
    /// Whether to use KV cache during inference
    pub use_cache: bool,
    /// Whether to use gradient checkpointing
    pub gradient_checkpointing: bool,
    /// Whether to tie word embeddings with output projection
    pub tie_word_embeddings: bool,
    
    // Optimization features
    /// Whether to use gated feed-forward networks
    pub use_gated_ffn: bool,
    /// Whether to use parallel attention and FFN
    pub parallel_attn_ffn: bool,
    /// Whether to use bias in linear layers
    pub use_bias: bool,
    
    // Model-specific parameters
    /// Number of encoder layers (for encoder-decoder models)
    pub num_encoder_layers: Option<usize>,
    /// Number of decoder layers (for encoder-decoder models)
    pub num_decoder_layers: Option<usize>,
    /// Cross-attention dropout (for encoder-decoder models)
    pub cross_attention_dropout: Option<f32>,
}

impl Default for TransformerConfig {
    fn default() -> Self {
        Self {
            model_type: ModelType::default(),
            vocab_size: 50257,
            hidden_size: 768,
            num_layers: 12,
            num_attention_heads: 12,
            intermediate_size: 3072,
            max_position_embeddings: 1024,
            
            attention_dropout: 0.1,
            attention_bias: true,
            attention_scale: None,
            
            dropout: 0.1,
            activation_function: ActivationType::default(),
            normalization_type: NormalizationType::default(),
            position_encoding_type: PositionEncodingType::default(),
            pre_norm: false,
            layer_norm_eps: 1e-5,
            
            use_cache: true,
            gradient_checkpointing: false,
            tie_word_embeddings: true,
            
            use_gated_ffn: false,
            parallel_attn_ffn: false,
            use_bias: true,
            
            num_encoder_layers: None,
            num_decoder_layers: None,
            cross_attention_dropout: None,
        }
    }
}

impl TransformerConfig {
    /// Create a new configuration with basic parameters.
    pub fn new(
        vocab_size: usize,
        hidden_size: usize,
        num_layers: usize,
        num_attention_heads: usize,
    ) -> Self {
        Self {
            vocab_size,
            hidden_size,
            num_layers,
            num_attention_heads,
            intermediate_size: hidden_size * 4, // Standard 4x expansion
            ..Default::default()
        }
    }
    
    /// Create a GPT-style decoder-only configuration.
    pub fn gpt_style(
        vocab_size: usize,
        hidden_size: usize,
        num_layers: usize,
        num_attention_heads: usize,
    ) -> Self {
        Self {
            model_type: ModelType::DecoderOnly,
            vocab_size,
            hidden_size,
            num_layers,
            num_attention_heads,
            intermediate_size: hidden_size * 4,
            pre_norm: true, // GPT uses pre-norm
            ..Default::default()
        }
    }
    
    /// Create a BERT-style encoder-only configuration.
    pub fn bert_style(
        vocab_size: usize,
        hidden_size: usize,
        num_layers: usize,
        num_attention_heads: usize,
    ) -> Self {
        Self {
            model_type: ModelType::EncoderOnly,
            vocab_size,
            hidden_size,
            num_layers,
            num_attention_heads,
            intermediate_size: hidden_size * 4,
            pre_norm: false, // BERT uses post-norm
            ..Default::default()
        }
    }
    
    /// Create a T5-style encoder-decoder configuration.
    pub fn t5_style(
        vocab_size: usize,
        hidden_size: usize,
        num_encoder_layers: usize,
        num_decoder_layers: usize,
        num_attention_heads: usize,
    ) -> Self {
        Self {
            model_type: ModelType::EncoderDecoder,
            vocab_size,
            hidden_size,
            num_layers: num_encoder_layers + num_decoder_layers,
            num_attention_heads,
            intermediate_size: hidden_size * 4,
            num_encoder_layers: Some(num_encoder_layers),
            num_decoder_layers: Some(num_decoder_layers),
            normalization_type: NormalizationType::RMSNorm,
            position_encoding_type: PositionEncodingType::RelativeBias,
            pre_norm: true,
            use_bias: false,
            ..Default::default()
        }
    }
    
    /// Get the dimension per attention head.
    pub fn head_dim(&self) -> usize {
        self.hidden_size / self.num_attention_heads
    }
    
    /// Validate the configuration.
    pub fn validate(&self) -> Result<(), String> {
        if self.hidden_size % self.num_attention_heads != 0 {
            return Err(format!(
                "hidden_size ({}) must be divisible by num_attention_heads ({})",
                self.hidden_size, self.num_attention_heads
            ));
        }
        
        if self.vocab_size == 0 {
            return Err("vocab_size must be greater than 0".to_string());
        }
        
        if self.num_layers == 0 {
            return Err("num_layers must be greater than 0".to_string());
        }
        
        if self.num_attention_heads == 0 {
            return Err("num_attention_heads must be greater than 0".to_string());
        }
        
        if self.intermediate_size == 0 {
            return Err("intermediate_size must be greater than 0".to_string());
        }
        
        // Validate encoder-decoder specific settings
        if self.model_type == ModelType::EncoderDecoder {
            if self.num_encoder_layers.is_none() || self.num_decoder_layers.is_none() {
                return Err("num_encoder_layers and num_decoder_layers must be specified for encoder-decoder models".to_string());
            }
        }
        
        Ok(())
    }

    // Builder methods for configuration

    /// Set the model type.
    pub fn with_model_type(mut self, model_type: ModelType) -> Self {
        self.model_type = model_type;
        self
    }

    /// Set the activation function.
    pub fn with_activation(mut self, activation: ActivationType) -> Self {
        self.activation_function = activation;
        self
    }

    /// Set the normalization type.
    pub fn with_normalization(mut self, norm_type: NormalizationType) -> Self {
        self.normalization_type = norm_type;
        self
    }

    /// Set the position encoding type.
    pub fn with_position_encoding(mut self, pos_type: PositionEncodingType) -> Self {
        self.position_encoding_type = pos_type;
        self
    }

    /// Set dropout rates.
    pub fn with_dropout(mut self, dropout: f32, attention_dropout: Option<f32>) -> Self {
        self.dropout = dropout;
        if let Some(attn_dropout) = attention_dropout {
            self.attention_dropout = attn_dropout;
        }
        self
    }

    /// Enable or disable pre-normalization.
    pub fn with_pre_norm(mut self, pre_norm: bool) -> Self {
        self.pre_norm = pre_norm;
        self
    }

    /// Enable or disable KV cache.
    pub fn with_cache(mut self, use_cache: bool) -> Self {
        self.use_cache = use_cache;
        self
    }

    /// Enable or disable gated FFN.
    pub fn with_gated_ffn(mut self, use_gated_ffn: bool) -> Self {
        self.use_gated_ffn = use_gated_ffn;
        self
    }

    /// Enable or disable bias in layers.
    pub fn with_bias(mut self, use_bias: bool) -> Self {
        self.use_bias = use_bias;
        self
    }

    /// Set custom intermediate size.
    pub fn with_intermediate_size(mut self, intermediate_size: usize) -> Self {
        self.intermediate_size = intermediate_size;
        self
    }

    /// Set maximum position embeddings.
    pub fn with_max_position_embeddings(mut self, max_pos: usize) -> Self {
        self.max_position_embeddings = max_pos;
        self
    }

    /// Set layer normalization epsilon.
    pub fn with_layer_norm_eps(mut self, eps: f32) -> Self {
        self.layer_norm_eps = eps;
        self
    }

    /// Enable gradient checkpointing.
    pub fn with_gradient_checkpointing(mut self, enabled: bool) -> Self {
        self.gradient_checkpointing = enabled;
        self
    }

    /// Set whether to tie word embeddings.
    pub fn with_tie_word_embeddings(mut self, tie: bool) -> Self {
        self.tie_word_embeddings = tie;
        self
    }

    /// Enable parallel attention and FFN computation.
    pub fn with_parallel_attn_ffn(mut self, parallel: bool) -> Self {
        self.parallel_attn_ffn = parallel;
        self
    }

    /// Set custom attention scale.
    pub fn with_attention_scale(mut self, scale: Option<f32>) -> Self {
        self.attention_scale = scale;
        self
    }

    /// Set encoder-decoder layer counts.
    pub fn with_encoder_decoder_layers(
        mut self,
        num_encoder_layers: usize,
        num_decoder_layers: usize
    ) -> Self {
        self.model_type = ModelType::EncoderDecoder;
        self.num_encoder_layers = Some(num_encoder_layers);
        self.num_decoder_layers = Some(num_decoder_layers);
        self.num_layers = num_encoder_layers + num_decoder_layers;
        self
    }
}

/// Configuration presets for common model architectures.
impl TransformerConfig {
    /// GPT-2 small configuration (117M parameters).
    pub fn gpt2_small() -> Self {
        Self::gpt_style(50257, 768, 12, 12)
            .with_max_position_embeddings(1024)
    }

    /// GPT-2 medium configuration (345M parameters).
    pub fn gpt2_medium() -> Self {
        Self::gpt_style(50257, 1024, 24, 16)
            .with_max_position_embeddings(1024)
    }

    /// GPT-2 large configuration (762M parameters).
    pub fn gpt2_large() -> Self {
        Self::gpt_style(50257, 1280, 36, 20)
            .with_max_position_embeddings(1024)
    }

    /// GPT-2 XL configuration (1.5B parameters).
    pub fn gpt2_xl() -> Self {
        Self::gpt_style(50257, 1600, 48, 25)
            .with_max_position_embeddings(1024)
    }

    /// BERT base configuration (110M parameters).
    pub fn bert_base() -> Self {
        Self::bert_style(30522, 768, 12, 12)
            .with_max_position_embeddings(512)
    }

    /// BERT large configuration (340M parameters).
    pub fn bert_large() -> Self {
        Self::bert_style(30522, 1024, 24, 16)
            .with_max_position_embeddings(512)
    }

    /// T5 small configuration (60M parameters).
    pub fn t5_small() -> Self {
        Self::t5_style(32128, 512, 6, 6, 8)
            .with_max_position_embeddings(512)
    }

    /// T5 base configuration (220M parameters).
    pub fn t5_base() -> Self {
        Self::t5_style(32128, 768, 12, 12, 12)
            .with_max_position_embeddings(512)
    }

    /// T5 large configuration (770M parameters).
    pub fn t5_large() -> Self {
        Self::t5_style(32128, 1024, 24, 24, 16)
            .with_max_position_embeddings(512)
    }
}
