//! Transformer model implementations.
//!
//! This module provides complete Transformer architecture implementations including:
//! - Encoder and decoder layers
//! - Position encoding systems
//! - Model variants (GPT, BERT, T5, etc.)
//! - Configuration management

use crate::tensor::Numeric;
use crate::error::TensorError;

pub mod config;
pub mod encoder;
pub mod decoder;
pub mod block;
pub mod position;
pub mod variants;

#[cfg(test)]
mod tests;

// Re-export main types
pub use config::*;
pub use encoder::*;
pub use decoder::*;
pub use block::*;
pub use position::*;

/// Transformer model error type.
pub type TransformerError = TensorError;

