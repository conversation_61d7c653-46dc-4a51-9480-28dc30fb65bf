//! Unit tests for Transformer components.

#[cfg(test)]
mod tests {
    use super::super::*;
    use crate::tensor::{<PERSON><PERSON><PERSON>, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
    use crate::layers::ParameterInit;
    use config::*;
    use encoder::*;
    use decoder::*;
    use block::*;
    use position::*;
    use variants::*;

    /// Test Transformer configuration creation and validation.
    #[test]
    fn test_transformer_config() {
        // Test basic configuration
        let config = TransformerConfig::new(
            1000,  // vocab_size
            512,   // hidden_size
            6,     // num_layers
            8,     // num_attention_heads
        );

        assert_eq!(config.model_type, ModelType::default());
        assert_eq!(config.vocab_size, 1000);
        assert_eq!(config.hidden_size, 512);
        assert_eq!(config.num_layers, 6);
        assert_eq!(config.num_attention_heads, 8);
        
        // Test validation
        assert!(config.validate().is_ok());
        
        // Test invalid configuration (hidden_size not divisible by num_attention_heads)
        let invalid_config = TransformerConfig::new(
            1000,
            513,  // Not divisible by 8
            6,
            8,
        );
        assert!(invalid_config.validate().is_err());
    }
    
    /// Test preset configurations.
    #[test]
    fn test_preset_configs() {
        let gpt2_small = TransformerConfig::gpt2_small();
        assert_eq!(gpt2_small.model_type, ModelType::DecoderOnly);
        assert_eq!(gpt2_small.vocab_size, 50257);
        assert_eq!(gpt2_small.hidden_size, 768);
        assert_eq!(gpt2_small.num_layers, 12);
        assert_eq!(gpt2_small.num_attention_heads, 12);
        
        let bert_base = TransformerConfig::bert_base();
        assert_eq!(bert_base.model_type, ModelType::EncoderOnly);
        assert_eq!(bert_base.vocab_size, 30522);
        assert_eq!(bert_base.hidden_size, 768);
        assert_eq!(bert_base.num_layers, 12);
        assert_eq!(bert_base.num_attention_heads, 12);
        
        let t5_base = TransformerConfig::t5_base();
        assert_eq!(t5_base.model_type, ModelType::EncoderDecoder);
        assert_eq!(t5_base.vocab_size, 32128);
        assert_eq!(t5_base.hidden_size, 768);
        assert_eq!(t5_base.num_layers, 12);
        assert_eq!(t5_base.num_attention_heads, 12);
    }
    
    /// Test encoder layer creation and forward pass.
    #[test]
    fn test_encoder_layer() {
        let config = TransformerConfig::gpt2_small();
        let layer_config = EncoderLayerConfig::from(&config);
        
        let mut encoder_layer = TransformerEncoderLayer::<f32>::new(layer_config).unwrap();
        encoder_layer.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        // Test forward pass
        let batch_size = 2;
        let seq_len = 10;
        let hidden_size = config.hidden_size;
        
        let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let output = encoder_layer.forward(&input, None).unwrap();
        
        // Check output shape
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
        
        // Check that output is different from input (transformation occurred)
        let input_data = input.data();
        let output_data = output.data();
        let mut different_count = 0;
        for (i, o) in input_data.iter().zip(output_data.iter()) {
            if (i - o).abs() > 1e-6 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Output should be different from input");
    }
    
    /// Test decoder layer creation and forward pass.
    #[test]
    fn test_decoder_layer() {
        let config = TransformerConfig::gpt2_small();
        let layer_config = DecoderLayerConfig::from(&config);
        
        let mut decoder_layer = TransformerDecoderLayer::<f32>::new(layer_config).unwrap();
        decoder_layer.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        // Test forward pass (decoder-only)
        let batch_size = 2;
        let seq_len = 10;
        let hidden_size = config.hidden_size;
        
        let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let output = decoder_layer.forward(&input, None, None, None).unwrap();
        
        // Check output shape
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    }
    
    /// Test encoder-decoder interaction.
    #[test]
    fn test_encoder_decoder_interaction() {
        let config = TransformerConfig::t5_base();
        let encoder_config = EncoderLayerConfig::from(&config);
        let decoder_config = DecoderLayerConfig::from(&config);
        
        let mut encoder_layer = TransformerEncoderLayer::<f32>::new(encoder_config).unwrap();
        let mut decoder_layer = TransformerDecoderLayer::<f32>::new(decoder_config).unwrap();
        
        encoder_layer.init_parameters(ParameterInit::XavierUniform).unwrap();
        decoder_layer.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        let batch_size = 2;
        let encoder_seq_len = 8;
        let decoder_seq_len = 6;
        let hidden_size = config.hidden_size;
        
        // Encoder forward pass
        let encoder_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, encoder_seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let encoder_output = encoder_layer.forward(&encoder_input, None).unwrap();
        
        // Decoder forward pass with cross-attention
        let decoder_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, decoder_seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let decoder_output = decoder_layer.forward(&decoder_input, Some(&encoder_output), None, None).unwrap();
        
        // Check output shapes
        assert_eq!(encoder_output.shape().dims(), &[batch_size, encoder_seq_len, hidden_size]);
        assert_eq!(decoder_output.shape().dims(), &[batch_size, decoder_seq_len, hidden_size]);
    }
    
    /// Test multi-layer encoder.
    #[test]
    fn test_multi_layer_encoder() {
        let config = TransformerConfig::bert_base();
        let mut encoder = TransformerEncoder::<f32>::new(&config).unwrap();
        encoder.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        let batch_size = 2;
        let seq_len = 16;
        let hidden_size = config.hidden_size;
        
        let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let output = encoder.forward(&input, None).unwrap();
        
        // Check output shape
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
        
        // Check number of layers
        assert_eq!(encoder.num_layers(), config.num_layers);
    }
    
    /// Test multi-layer decoder.
    #[test]
    fn test_multi_layer_decoder() {
        let config = TransformerConfig::gpt2_small();
        let mut decoder = TransformerDecoder::<f32>::new(&config).unwrap();
        decoder.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        let batch_size = 2;
        let seq_len = 16;
        let hidden_size = config.hidden_size;
        
        let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let output = decoder.forward(&input, None, None, None).unwrap();
        
        // Check output shape
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
        
        // Check number of layers
        assert_eq!(decoder.num_layers(), config.num_layers);
        assert!(decoder.is_decoder_only());
    }
    
    /// Test Transformer block creation and forward pass.
    #[test]
    fn test_transformer_block() {
        // Test decoder-only block (GPT-style)
        let gpt_config = TransformerConfig::gpt2_small();
        let mut gpt_block = TransformerBlock::<f32>::new(gpt_config.clone()).unwrap();
        gpt_block.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        assert_eq!(gpt_block.block_type(), BlockType::DecoderOnly);
        assert!(!gpt_block.has_encoder());
        assert!(gpt_block.has_decoder());
        
        let batch_size = 2;
        let seq_len = 10;
        let hidden_size = gpt_config.hidden_size;
        
        let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let output = gpt_block.forward(&input, None, None, None).unwrap();
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
        
        // Test encoder-only block (BERT-style)
        let bert_config = TransformerConfig::bert_base();
        let mut bert_block = TransformerBlock::<f32>::new(bert_config.clone()).unwrap();
        bert_block.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        assert_eq!(bert_block.block_type(), BlockType::EncoderOnly);
        assert!(bert_block.has_encoder());
        assert!(!bert_block.has_decoder());
        
        let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let output = bert_block.forward(&input, None, None, None).unwrap();
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
        
        // Test encoder-decoder block (T5-style)
        let t5_config = TransformerConfig::t5_base();
        let mut t5_block = TransformerBlock::<f32>::new(t5_config.clone()).unwrap();
        t5_block.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        assert_eq!(t5_block.block_type(), BlockType::EncoderDecoder);
        assert!(t5_block.has_encoder());
        assert!(t5_block.has_decoder());
        
        let encoder_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0).unwrap();
        let decoder_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len - 2, hidden_size]), 0.0, 1.0).unwrap();
        let output = t5_block.forward(&decoder_input, Some(&encoder_input), None, None).unwrap();
        assert_eq!(output.shape().dims(), &[batch_size, seq_len - 2, hidden_size]);
    }
    
    /// Test position encoding implementations.
    #[test]
    fn test_position_encodings() {
        let max_length = 100;
        let d_model = 512;
        
        // Test learned position embeddings
        let learned_config = PositionEncodingConfig::new(PositionEncodingType::Learned, max_length, d_model);
        let mut learned_pos = LearnedPositionEmbedding::<f32>::new(learned_config).unwrap();
        learned_pos.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 }).unwrap();
        
        let batch_size = 2;
        let seq_len = 50;
        let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0).unwrap();
        let output = learned_pos.encode(&input, None).unwrap();
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, d_model]);
        
        // Test sinusoidal position encoding
        let sin_config = PositionEncodingConfig::new(PositionEncodingType::Sinusoidal, max_length, d_model);
        let sin_pos = SinusoidalPositionEncoding::<f32>::new(sin_config).unwrap();
        
        let output = sin_pos.encode(&input, None).unwrap();
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, d_model]);
        
        // Test RoPE
        let rope_config = PositionEncodingConfig::new(PositionEncodingType::RoPE, max_length, d_model);
        let rope_pos = RotaryPositionEmbedding::<f32>::new(rope_config).unwrap();
        
        let output = rope_pos.encode(&input, None).unwrap();
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, d_model]);
    }
}
