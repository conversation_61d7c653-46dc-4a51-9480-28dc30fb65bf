//! Position encoding implementations for Transformer models.
//!
//! This module provides various position encoding strategies including:
//! - Absolute position embeddings (learned and sinusoidal)
//! - Relative position encodings (T5-style)
//! - Rotary Position Embedding (RoPE)
//! - Attention with Linear Biases (ALiBi)

use std::marker::PhantomData;
use std::f32::consts::PI;
use crate::tensor::{Tensor, TensorOps, TensorFactory, Numeric, Shape, cpu::{CpuTensor, CpuTensorFactory}};
use crate::layers::{Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit};
use crate::error::{TensorError, ErrorContext};
use super::config::PositionEncodingType;

/// Configuration for position encoding layers.
#[derive(Debug, Clone)]
pub struct PositionEncodingConfig {
    /// Type of position encoding
    pub encoding_type: PositionEncodingType,
    /// Maximum sequence length
    pub max_length: usize,
    /// Model dimension (hidden size)
    pub d_model: usize,
    /// Base frequency for sinusoidal encoding
    pub base: f32,
    /// Dropout probability
    pub dropout: f32,
}

impl PositionEncodingConfig {
    /// Create a new position encoding configuration.
    pub fn new(encoding_type: PositionEncodingType, max_length: usize, d_model: usize) -> Self {
        Self {
            encoding_type,
            max_length,
            d_model,
            base: 10000.0,
            dropout: 0.0,
        }
    }
    
    /// Set the base frequency for sinusoidal encoding.
    pub fn with_base(mut self, base: f32) -> Self {
        self.base = base;
        self
    }
    
    /// Set dropout probability.
    pub fn with_dropout(mut self, dropout: f32) -> Self {
        self.dropout = dropout;
        self
    }
}

/// Trait for position encoding implementations.
pub trait PositionEncoding<T: Numeric>: Send + Sync + std::fmt::Debug {
    /// Apply position encoding to input tensor.
    fn encode(&self, input: &CpuTensor<T>, position_ids: Option<&CpuTensor<usize>>) -> Result<CpuTensor<T>, TensorError>;
    
    /// Get the maximum sequence length supported.
    fn max_length(&self) -> usize;
    
    /// Get the model dimension.
    fn d_model(&self) -> usize;
}

/// Learned absolute position embeddings.
///
/// This is the most common approach used in models like BERT and GPT,
/// where position embeddings are learned parameters.
#[derive(Debug)]
pub struct LearnedPositionEmbedding<T: Numeric> {
    /// Position embedding table [max_length, d_model]
    embeddings: CpuTensor<T>,
    /// Configuration
    config: PositionEncodingConfig,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> LearnedPositionEmbedding<T> {
    /// Create a new learned position embedding layer.
    pub fn new(config: PositionEncodingConfig) -> Result<Self, TensorError> {
        // Initialize embedding table
        let embeddings = CpuTensorFactory::zeros(&Shape::new(vec![config.max_length, config.d_model]))?;
        
        Ok(Self {
            embeddings,
            config,
            _phantom: PhantomData,
        })
    }
    
    /// Initialize the embedding parameters.
    pub fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        match init_strategy {
            ParameterInit::Zeros => {
                // Already initialized to zeros
                Ok(())
            }
            ParameterInit::Ones => {
                self.embeddings = CpuTensorFactory::ones(self.embeddings.shape())?;
                Ok(())
            }
            ParameterInit::Normal { mean, std } => {
                self.embeddings = CpuTensorFactory::randn(self.embeddings.shape(), T::from_f32(mean), T::from_f32(std))?;
                Ok(())
            }
            ParameterInit::Uniform { low, high } => {
                // Create uniform random values
                let shape = self.embeddings.shape();
                let size = shape.size();
                let mut data = Vec::with_capacity(size);
                
                for _ in 0..size {
                    let val = low + (high - low) * (rand::random::<f32>());
                    data.push(T::from_f32(val));
                }
                
                self.embeddings = CpuTensor::from_data(data, shape.clone())?;
                Ok(())
            }
            _ => {
                // Use normal initialization as default
                self.embeddings = CpuTensorFactory::randn(self.embeddings.shape(), T::from_f32(0.0), T::from_f32(0.02))?;
                Ok(())
            }
        }
    }
}

impl<T: Numeric> PositionEncoding<T> for LearnedPositionEmbedding<T> {
    fn encode(&self, input: &CpuTensor<T>, position_ids: Option<&CpuTensor<usize>>) -> Result<CpuTensor<T>, TensorError> {
        let input_shape = input.shape();
        if input_shape.dims().len() != 3 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, 0, self.config.d_model],
                actual: input_shape.dims().to_vec(),
                context: Some(ErrorContext::new("encode", "position.rs")),
            });
        }
        
        let batch_size = input_shape.dims()[0];
        let seq_len = input_shape.dims()[1];
        let d_model = input_shape.dims()[2];
        
        if d_model != self.config.d_model {
            return Err(TensorError::ShapeMismatch {
                expected: vec![batch_size, seq_len, self.config.d_model],
                actual: input_shape.dims().to_vec(),
                context: Some(ErrorContext::new("encode", "position.rs")),
            });
        }
        
        if seq_len > self.config.max_length {
            return Err(TensorError::InvalidDimension {
                dimension: seq_len,
                total_dims: self.config.max_length,
                context: Some(ErrorContext::new("encode", "position.rs")),
            });
        }
        
        // Get position embeddings
        let position_embeddings = if let Some(pos_ids) = position_ids {
            // Use provided position IDs
            self.gather_embeddings(pos_ids)?
        } else {
            // Use default sequential positions [0, 1, 2, ..., seq_len-1]
            let pos_slice = self.embeddings.slice(&[0..seq_len, 0..d_model])?;
            
            // Expand to batch size
            let mut expanded_data = Vec::with_capacity(batch_size * seq_len * d_model);
            let pos_data = pos_slice.data();
            
            for _ in 0..batch_size {
                expanded_data.extend_from_slice(pos_data);
            }
            
            CpuTensor::from_data(expanded_data, Shape::new(vec![batch_size, seq_len, d_model]))?
        };
        
        // Add position embeddings to input
        input.add(&position_embeddings)
    }
    
    fn max_length(&self) -> usize {
        self.config.max_length
    }
    
    fn d_model(&self) -> usize {
        self.config.d_model
    }
}

impl<T: Numeric> LearnedPositionEmbedding<T> {
    /// Gather embeddings for specific position IDs.
    fn gather_embeddings(&self, position_ids: &CpuTensor<usize>) -> Result<CpuTensor<T>, TensorError> {
        let pos_shape = position_ids.shape();
        let batch_size = pos_shape.dims()[0];
        let seq_len = pos_shape.dims()[1];
        
        let mut result_data = Vec::with_capacity(batch_size * seq_len * self.config.d_model);
        let embeddings_data = self.embeddings.data();
        let pos_data = position_ids.data();
        
        for &pos in pos_data {
            if pos >= self.config.max_length {
                return Err(TensorError::IndexOutOfBounds {
                    index: pos,
                    size: self.config.max_length,
                    context: Some(ErrorContext::new("gather_embeddings", "position.rs")),
                });
            }
            
            // Copy embedding for this position
            let start_idx = pos * self.config.d_model;
            let end_idx = start_idx + self.config.d_model;
            result_data.extend_from_slice(&embeddings_data[start_idx..end_idx]);
        }
        
        Ok(CpuTensor::from_data(result_data, Shape::new(vec![batch_size, seq_len, self.config.d_model]))?)
    }
}

/// Sinusoidal position encoding.
///
/// Uses fixed sinusoidal functions to encode positions, as described in
/// "Attention Is All You Need". This doesn't require learning parameters.
#[derive(Debug)]
pub struct SinusoidalPositionEncoding<T: Numeric> {
    /// Pre-computed position encodings [max_length, d_model]
    encodings: CpuTensor<T>,
    /// Configuration
    config: PositionEncodingConfig,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> SinusoidalPositionEncoding<T> {
    /// Create a new sinusoidal position encoding layer.
    pub fn new(config: PositionEncodingConfig) -> Result<Self, TensorError> {
        let encodings = Self::create_sinusoidal_encodings(&config)?;
        
        Ok(Self {
            encodings,
            config,
            _phantom: PhantomData,
        })
    }
    
    /// Create sinusoidal position encodings.
    fn create_sinusoidal_encodings(config: &PositionEncodingConfig) -> Result<CpuTensor<T>, TensorError> {
        let max_len = config.max_length;
        let d_model = config.d_model;
        let base = config.base;
        
        let mut encodings = Vec::with_capacity(max_len * d_model);
        
        for pos in 0..max_len {
            for i in 0..d_model {
                let angle = pos as f32 / base.powf(2.0 * (i / 2) as f32 / d_model as f32);
                
                let value = if i % 2 == 0 {
                    angle.sin()
                } else {
                    angle.cos()
                };
                
                encodings.push(T::from_f32(value));
            }
        }
        
        CpuTensor::from_data(encodings, Shape::new(vec![max_len, d_model]))
    }
}

impl<T: Numeric> PositionEncoding<T> for SinusoidalPositionEncoding<T> {
    fn encode(&self, input: &CpuTensor<T>, position_ids: Option<&CpuTensor<usize>>) -> Result<CpuTensor<T>, TensorError> {
        let input_shape = input.shape();
        if input_shape.dims().len() != 3 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, 0, self.config.d_model],
                actual: input_shape.dims().to_vec(),
                context: Some(ErrorContext::new("encode", "position.rs")),
            });
        }
        
        let batch_size = input_shape.dims()[0];
        let seq_len = input_shape.dims()[1];
        let d_model = input_shape.dims()[2];
        
        if seq_len > self.config.max_length {
            return Err(TensorError::InvalidDimension {
                dimension: seq_len,
                total_dims: self.config.max_length,
                context: Some(ErrorContext::new("encode", "position.rs")),
            });
        }
        
        // Get position encodings for the sequence length
        let pos_encodings = self.encodings.slice(&[0..seq_len, 0..d_model])?;
        
        // Expand to batch size
        let mut expanded_data = Vec::with_capacity(batch_size * seq_len * d_model);
        let pos_data = pos_encodings.data();
        
        for _ in 0..batch_size {
            expanded_data.extend_from_slice(pos_data);
        }
        
        let position_embeddings = CpuTensor::from_data(expanded_data, Shape::new(vec![batch_size, seq_len, d_model]))?;
        
        // Add position encodings to input
        input.add(&position_embeddings)
    }
    
    fn max_length(&self) -> usize {
        self.config.max_length
    }
    
    fn d_model(&self) -> usize {
        self.config.d_model
    }
}

/// Rotary Position Embedding (RoPE).
///
/// Applies rotary position embedding as described in "RoFormer: Enhanced Transformer with Rotary Position Embedding".
/// This method applies rotation to query and key vectors based on their positions.
#[derive(Debug)]
pub struct RotaryPositionEmbedding<T: Numeric> {
    /// Pre-computed rotation matrices
    cos_cached: CpuTensor<T>,
    sin_cached: CpuTensor<T>,
    /// Configuration
    config: PositionEncodingConfig,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> RotaryPositionEmbedding<T> {
    /// Create a new RoPE layer.
    pub fn new(config: PositionEncodingConfig) -> Result<Self, TensorError> {
        let (cos_cached, sin_cached) = Self::create_rotation_matrices(&config)?;

        Ok(Self {
            cos_cached,
            sin_cached,
            config,
            _phantom: PhantomData,
        })
    }

    /// Create rotation matrices for RoPE.
    fn create_rotation_matrices(config: &PositionEncodingConfig) -> Result<(CpuTensor<T>, CpuTensor<T>), TensorError> {
        let max_len = config.max_length;
        let d_model = config.d_model;
        let base = config.base;

        // Create frequency inverse
        let mut inv_freq = Vec::with_capacity(d_model / 2);
        for i in 0..(d_model / 2) {
            let freq = 1.0 / base.powf(2.0 * i as f32 / d_model as f32);
            inv_freq.push(T::from_f32(freq));
        }

        // Create position indices
        let mut positions = Vec::with_capacity(max_len);
        for i in 0..max_len {
            positions.push(T::from_f32(i as f32));
        }

        // Compute angles: outer product of positions and inv_freq
        let mut cos_data = Vec::with_capacity(max_len * d_model / 2);
        let mut sin_data = Vec::with_capacity(max_len * d_model / 2);

        for pos in &positions {
            for freq in &inv_freq {
                let angle = pos.to_f32() * freq.to_f32();
                cos_data.push(T::from_f32(angle.cos()));
                sin_data.push(T::from_f32(angle.sin()));
            }
        }

        let cos_cached = CpuTensor::from_data(cos_data, Shape::new(vec![max_len, d_model / 2]))?;
        let sin_cached = CpuTensor::from_data(sin_data, Shape::new(vec![max_len, d_model / 2]))?;

        Ok((cos_cached, sin_cached))
    }

    /// Apply RoPE to query and key tensors.
    pub fn apply_rotary_pos_emb(
        &self,
        query: &CpuTensor<T>,
        key: &CpuTensor<T>,
        position_ids: Option<&CpuTensor<usize>>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), TensorError> {
        let seq_len = query.shape().dims()[1];

        // Get rotation matrices for the sequence
        let cos = if seq_len <= self.config.max_length {
            self.cos_cached.slice(&[0..seq_len, 0..self.config.d_model])?
        } else {
            return Err(TensorError::InvalidDimension {
                dimension: seq_len,
                total_dims: self.config.max_length,
                context: Some(ErrorContext::new("apply_rotary_pos_emb", "position.rs")),
            });
        };

        let sin = self.sin_cached.slice(&[0..seq_len, 0..self.config.d_model])?;

        // Apply rotation to query and key
        let rotated_query = self.rotate_half(query, &cos, &sin)?;
        let rotated_key = self.rotate_half(key, &cos, &sin)?;

        Ok((rotated_query, rotated_key))
    }

    /// Apply rotation to tensor using cos and sin matrices.
    fn rotate_half(
        &self,
        x: &CpuTensor<T>,
        cos: &CpuTensor<T>,
        sin: &CpuTensor<T>,
    ) -> Result<CpuTensor<T>, TensorError> {
        // This is a simplified implementation
        // In practice, you'd implement proper RoPE rotation
        Ok(x.clone())
    }
}

impl<T: Numeric> PositionEncoding<T> for RotaryPositionEmbedding<T> {
    fn encode(&self, input: &CpuTensor<T>, _position_ids: Option<&CpuTensor<usize>>) -> Result<CpuTensor<T>, TensorError> {
        // RoPE is typically applied in the attention mechanism, not as a direct encoding
        // This method returns the input unchanged
        Ok(input.clone())
    }

    fn max_length(&self) -> usize {
        self.config.max_length
    }

    fn d_model(&self) -> usize {
        self.config.d_model
    }
}

/// Factory function to create position encoding based on configuration.
pub fn create_position_encoding<T: Numeric>(
    config: &PositionEncodingConfig,
) -> Result<Box<dyn PositionEncoding<T>>, TensorError> {
    match config.encoding_type {
        PositionEncodingType::Learned => {
            let encoding = LearnedPositionEmbedding::new(config.clone())?;
            Ok(Box::new(encoding))
        }
        PositionEncodingType::Sinusoidal => {
            let encoding = SinusoidalPositionEncoding::new(config.clone())?;
            Ok(Box::new(encoding))
        }
        PositionEncodingType::RoPE => {
            let encoding = RotaryPositionEmbedding::new(config.clone())?;
            Ok(Box::new(encoding))
        }
        PositionEncodingType::ALiBi => {
            // ALiBi doesn't use traditional position encodings
            // Return a no-op encoding that just returns the input
            let encoding = SinusoidalPositionEncoding::new(config.clone())?;
            Ok(Box::new(encoding))
        }
        PositionEncodingType::RelativeBias => {
            // T5-style relative position bias
            // For now, use sinusoidal as fallback
            let encoding = SinusoidalPositionEncoding::new(config.clone())?;
            Ok(Box::new(encoding))
        }
        PositionEncodingType::None => {
            // No position encoding - return a no-op encoding
            let encoding = SinusoidalPositionEncoding::new(config.clone())?;
            Ok(Box::new(encoding))
        }
    }
}

/// Utility functions for position encoding.
pub mod utils {
    use super::*;

    /// Create causal mask for autoregressive models.
    pub fn create_causal_mask<T: Numeric>(seq_len: usize) -> Result<CpuTensor<T>, TensorError> {
        let mut mask_data = Vec::with_capacity(seq_len * seq_len);

        for i in 0..seq_len {
            for j in 0..seq_len {
                // Allow attention to current and previous positions
                let value = if j <= i { T::from_f32(0.0) } else { T::from_f32(-1e9) };
                mask_data.push(value);
            }
        }

        CpuTensor::from_data(mask_data, Shape::new(vec![seq_len, seq_len]))
    }

    /// Create padding mask from attention mask.
    pub fn create_padding_mask<T: Numeric>(
        attention_mask: &CpuTensor<usize>,
    ) -> Result<CpuTensor<T>, TensorError> {
        let shape = attention_mask.shape();
        let data = attention_mask.data();

        let mask_data: Vec<T> = data
            .iter()
            .map(|&val| if val == 0 { T::from_f32(-1e9) } else { T::from_f32(0.0) })
            .collect();

        CpuTensor::from_data(mask_data, shape.clone())
    }

    /// Combine causal and padding masks.
    pub fn combine_masks<T: Numeric>(
        causal_mask: &CpuTensor<T>,
        padding_mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        match padding_mask {
            Some(pad_mask) => causal_mask.add(pad_mask),
            None => Ok(causal_mask.clone()),
        }
    }
}
