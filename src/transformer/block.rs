//! Transformer block implementations.
//!
//! This module provides unified interfaces for different Transformer architectures,
//! combining encoder and decoder components as needed.

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, Numeric, Shape, cpu::{<PERSON><PERSON>Tensor, CpuTensorFactory}};
use crate::layers::{Layer, ParameterizedLayer, ParameterInit};
use crate::error::{TensorError, ErrorContext};
use super::config::{TransformerConfig, ModelType};
use super::encoder::{TransformerEncoder, TransformerEncoderLayer};
use super::decoder::{TransformerDecoder, TransformerDecoderLayer};

/// Type of Transformer block.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BlockType {
    /// Encoder-only block (like BERT)
    EncoderOnly,
    /// Decoder-only block (like GPT)
    DecoderOnly,
    /// Encoder-decoder block (like T5)
    EncoderDecoder,
}

impl From<ModelType> for BlockType {
    fn from(model_type: ModelType) -> Self {
        match model_type {
            ModelType::EncoderOnly => BlockType::EncoderOnly,
            ModelType::DecoderOnly => BlockType::DecoderOnly,
            ModelType::EncoderDecoder => BlockType::EncoderDecoder,
        }
    }
}

/// Unified Transformer block that can represent different architectures.
///
/// This provides a single interface for encoder-only, decoder-only, and
/// encoder-decoder Transformer models.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::transformer::{TransformerBlock, TransformerConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
/// use qilin_inference::layers::ParameterInit;
///
/// // Create a GPT-style decoder-only block
/// let config = TransformerConfig::gpt2_small();
/// let mut block = TransformerBlock::new(config).unwrap();
/// block.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![2, 128, 768]), 0.0, 1.0).unwrap();
/// let output = block.forward(&input, None, None, None).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 128, 768]);
/// ```
#[derive(Debug)]
pub struct TransformerBlock<T: Numeric> {
    /// Type of this block
    block_type: BlockType,
    /// Encoder component (if present)
    encoder: Option<TransformerEncoder<T>>,
    /// Decoder component (if present)
    decoder: Option<TransformerDecoder<T>>,
    /// Configuration
    config: TransformerConfig,
    /// Training mode flag
    training: bool,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> TransformerBlock<T> {
    /// Create a new Transformer block from configuration.
    pub fn new(config: TransformerConfig) -> Result<Self, TensorError> {
        // Validate configuration
        config.validate().map_err(|msg| TensorError::UnsupportedOperation {
            operation: format!("Invalid configuration: {}", msg),
            context: Some(ErrorContext::new("new", "block.rs")),
        })?;
        
        let block_type = BlockType::from(config.model_type);
        
        // Create components based on block type
        let (encoder, decoder) = match block_type {
            BlockType::EncoderOnly => {
                let encoder = TransformerEncoder::new(&config)?;
                (Some(encoder), None)
            }
            BlockType::DecoderOnly => {
                let decoder = TransformerDecoder::new(&config)?;
                (None, Some(decoder))
            }
            BlockType::EncoderDecoder => {
                let encoder = TransformerEncoder::new(&config)?;
                let decoder = TransformerDecoder::new(&config)?;
                (Some(encoder), Some(decoder))
            }
        };
        
        Ok(Self {
            block_type,
            encoder,
            decoder,
            config,
            training: true,
            _phantom: PhantomData,
        })
    }
    
    /// Get the block type.
    pub fn block_type(&self) -> BlockType {
        self.block_type
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &TransformerConfig {
        &self.config
    }
    
    /// Check if this block has an encoder.
    pub fn has_encoder(&self) -> bool {
        self.encoder.is_some()
    }
    
    /// Check if this block has a decoder.
    pub fn has_decoder(&self) -> bool {
        self.decoder.is_some()
    }
    
    /// Get a reference to the encoder (if present).
    pub fn encoder(&self) -> Option<&TransformerEncoder<T>> {
        self.encoder.as_ref()
    }
    
    /// Get a reference to the decoder (if present).
    pub fn decoder(&self) -> Option<&TransformerDecoder<T>> {
        self.decoder.as_ref()
    }
    
    /// Get a mutable reference to the encoder (if present).
    pub fn encoder_mut(&mut self) -> Option<&mut TransformerEncoder<T>> {
        self.encoder.as_mut()
    }
    
    /// Get a mutable reference to the decoder (if present).
    pub fn decoder_mut(&mut self) -> Option<&mut TransformerDecoder<T>> {
        self.decoder.as_mut()
    }
    
    /// Initialize parameters for all components.
    pub fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        if let Some(ref mut encoder) = self.encoder {
            encoder.init_parameters(init_strategy.clone())?;
        }

        if let Some(ref mut decoder) = self.decoder {
            decoder.init_parameters(init_strategy)?;
        }
        
        Ok(())
    }
    
    /// Set training mode for all components.
    pub fn set_training(&mut self, training: bool) {
        self.training = training;
        
        if let Some(ref mut encoder) = self.encoder {
            encoder.set_training(training);
        }
        
        if let Some(ref mut decoder) = self.decoder {
            decoder.set_training(training);
        }
    }
    
    /// Get the total number of parameters.
    pub fn parameter_count(&self) -> usize {
        let mut count = 0;
        
        // Note: We would need to implement parameter_count for encoder/decoder
        // This is a placeholder implementation
        if self.encoder.is_some() {
            count += self.config.num_layers * (
                self.config.hidden_size * self.config.hidden_size * 4 + // attention projections
                self.config.hidden_size * self.config.intermediate_size * 2 + // FFN
                self.config.hidden_size * 4 // normalization and biases
            );
        }
        
        if self.decoder.is_some() {
            let decoder_layers = match self.config.model_type {
                ModelType::DecoderOnly => self.config.num_layers,
                ModelType::EncoderDecoder => self.config.num_decoder_layers.unwrap_or(self.config.num_layers / 2),
                _ => 0,
            };
            
            count += decoder_layers * (
                self.config.hidden_size * self.config.hidden_size * 4 + // self-attention
                if !matches!(self.config.model_type, ModelType::DecoderOnly) {
                    self.config.hidden_size * self.config.hidden_size * 4 // cross-attention
                } else { 0 } +
                self.config.hidden_size * self.config.intermediate_size * 2 + // FFN
                self.config.hidden_size * 6 // normalization and biases
            );
        }
        
        count
    }
    
    /// Forward pass through the Transformer block.
    ///
    /// The behavior depends on the block type:
    /// - EncoderOnly: Processes input through encoder, ignores decoder-specific arguments
    /// - DecoderOnly: Processes input through decoder, ignores encoder_input
    /// - EncoderDecoder: First processes encoder_input through encoder, then decoder_input through decoder
    ///
    /// # Arguments
    /// * `input` - Primary input tensor (decoder input for encoder-decoder, main input otherwise)
    /// * `encoder_input` - Optional encoder input (only used for encoder-decoder models)
    /// * `self_attention_mask` - Optional mask for self-attention
    /// * `cross_attention_mask` - Optional mask for cross-attention
    ///
    /// # Returns
    /// Output tensor from the final component in the pipeline
    pub fn forward(
        &self,
        input: &CpuTensor<T>,
        encoder_input: Option<&CpuTensor<T>>,
        self_attention_mask: Option<&CpuTensor<T>>,
        cross_attention_mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        match self.block_type {
            BlockType::EncoderOnly => {
                let encoder = self.encoder.as_ref().unwrap();
                encoder.forward(input, self_attention_mask)
            }
            
            BlockType::DecoderOnly => {
                let decoder = self.decoder.as_ref().unwrap();
                decoder.forward(input, None, self_attention_mask, None)
            }
            
            BlockType::EncoderDecoder => {
                let encoder = self.encoder.as_ref().unwrap();
                let decoder = self.decoder.as_ref().unwrap();
                
                // Use encoder_input if provided, otherwise use input for encoder
                let encoder_input_tensor = encoder_input.unwrap_or(input);
                
                // First pass through encoder
                let encoder_output = encoder.forward(encoder_input_tensor, cross_attention_mask)?;
                
                // Then pass through decoder with encoder output
                decoder.forward(input, Some(&encoder_output), self_attention_mask, cross_attention_mask)
            }
        }
    }
    
    /// Encode input (only for models with encoder).
    pub fn encode(
        &self,
        input: &CpuTensor<T>,
        attention_mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        match &self.encoder {
            Some(encoder) => encoder.forward(input, attention_mask),
            None => Err(TensorError::UnsupportedOperation {
                operation: "encode".to_string(),
                context: Some(ErrorContext::new("encode", "block.rs")),
            }),
        }
    }
    
    /// Decode input (only for models with decoder).
    pub fn decode(
        &self,
        input: &CpuTensor<T>,
        encoder_output: Option<&CpuTensor<T>>,
        self_attention_mask: Option<&CpuTensor<T>>,
        cross_attention_mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        match &self.decoder {
            Some(decoder) => decoder.forward(input, encoder_output, self_attention_mask, cross_attention_mask),
            None => Err(TensorError::UnsupportedOperation {
                operation: "decode".to_string(),
                context: Some(ErrorContext::new("decode", "block.rs")),
            }),
        }
    }
}

/// Factory methods for creating common Transformer architectures.
impl<T: Numeric> TransformerBlock<T> {
    /// Create a BERT-style encoder-only block.
    pub fn bert_style(
        vocab_size: usize,
        hidden_size: usize,
        num_layers: usize,
        num_attention_heads: usize,
    ) -> Result<Self, TensorError> {
        let config = TransformerConfig::bert_style(vocab_size, hidden_size, num_layers, num_attention_heads);
        Self::new(config)
    }

    /// Create a GPT-style decoder-only block.
    pub fn gpt_style(
        vocab_size: usize,
        hidden_size: usize,
        num_layers: usize,
        num_attention_heads: usize,
    ) -> Result<Self, TensorError> {
        let config = TransformerConfig::gpt_style(vocab_size, hidden_size, num_layers, num_attention_heads);
        Self::new(config)
    }

    /// Create a T5-style encoder-decoder block.
    pub fn t5_style(
        vocab_size: usize,
        hidden_size: usize,
        num_encoder_layers: usize,
        num_decoder_layers: usize,
        num_attention_heads: usize,
    ) -> Result<Self, TensorError> {
        let config = TransformerConfig::t5_style(
            vocab_size,
            hidden_size,
            num_encoder_layers,
            num_decoder_layers,
            num_attention_heads
        );
        Self::new(config)
    }

    /// Create a block from a preset configuration.
    pub fn from_preset(preset: &str) -> Result<Self, TensorError> {
        let config = match preset {
            "gpt2-small" => TransformerConfig::gpt2_small(),
            "gpt2-medium" => TransformerConfig::gpt2_medium(),
            "gpt2-large" => TransformerConfig::gpt2_large(),
            "gpt2-xl" => TransformerConfig::gpt2_xl(),
            "bert-base" => TransformerConfig::bert_base(),
            "bert-large" => TransformerConfig::bert_large(),
            "t5-small" => TransformerConfig::t5_small(),
            "t5-base" => TransformerConfig::t5_base(),
            "t5-large" => TransformerConfig::t5_large(),
            _ => return Err(TensorError::UnsupportedOperation {
                operation: format!("Unknown preset: {}", preset),
                context: Some(ErrorContext::new("from_preset", "block.rs")),
            }),
        };

        Self::new(config)
    }
}
