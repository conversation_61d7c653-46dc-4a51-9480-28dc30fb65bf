//! Transformer encoder implementation.
//!
//! This module provides the TransformerEncoderLayer and TransformerEncoder
//! implementations following the standard Transformer architecture.

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, Numeric, Shape, cpu::{<PERSON><PERSON><PERSON><PERSON>or, CpuTensorFactory}};
use crate::layers::{
    Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit,
    feedforward::{FeedForward, FeedForwardConfig},
    norm::{LayerNorm, LayerNormConfig, RMSNorm, RMSNormConfig},
    dropout::{Dropout, DropoutConfig},
    activation::ActivationType as LayerActivationType,
};
use crate::attention::{MultiHeadAttention, AttentionConfig};
use crate::error::{TensorError, ErrorContext};
use super::config::{TransformerConfig, ActivationType, NormalizationType};

/// Convert transformer ActivationType to layer ActivationType.
pub fn convert_activation_type(activation: ActivationType) -> LayerActivationType {
    match activation {
        ActivationType::ReLU => LayerActivationType::ReLU,
        ActivationType::GELU => LayerActivationType::GELU,
        ActivationType::Swish => LayerActivationType::Swish,
        ActivationType::SwiGLU => LayerActivationType::Swish, // Use Swish as fallback for SwiGLU
        ActivationType::GeGLU => LayerActivationType::GELU,   // Use GELU as fallback for GeGLU
        ActivationType::Tanh => LayerActivationType::Tanh,
    }
}

/// Configuration for a single Transformer encoder layer.
#[derive(Debug, Clone)]
pub struct EncoderLayerConfig {
    /// Hidden dimension size
    pub hidden_size: usize,
    /// Number of attention heads
    pub num_attention_heads: usize,
    /// Intermediate (FFN) dimension size
    pub intermediate_size: usize,
    /// Dropout probability
    pub dropout: f32,
    /// Attention dropout probability
    pub attention_dropout: f32,
    /// Activation function type
    pub activation_function: ActivationType,
    /// Normalization type
    pub normalization_type: NormalizationType,
    /// Whether to use pre-normalization
    pub pre_norm: bool,
    /// Layer normalization epsilon
    pub layer_norm_eps: f32,
    /// Whether to use bias in attention
    pub attention_bias: bool,
    /// Whether to use bias in linear layers
    pub use_bias: bool,
    /// Whether to use gated FFN
    pub use_gated_ffn: bool,
}

impl From<&TransformerConfig> for EncoderLayerConfig {
    fn from(config: &TransformerConfig) -> Self {
        Self {
            hidden_size: config.hidden_size,
            num_attention_heads: config.num_attention_heads,
            intermediate_size: config.intermediate_size,
            dropout: config.dropout,
            attention_dropout: config.attention_dropout,
            activation_function: config.activation_function,
            normalization_type: config.normalization_type,
            pre_norm: config.pre_norm,
            layer_norm_eps: config.layer_norm_eps,
            attention_bias: config.attention_bias,
            use_bias: config.use_bias,
            use_gated_ffn: config.use_gated_ffn,
        }
    }
}

/// Normalization layer trait for dynamic dispatch.
pub trait NormalizationLayer<T: Numeric>: Send + Sync + std::fmt::Debug {
    /// Forward pass through the normalization layer.
    fn forward(&self, input: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError>;
    
    /// Initialize parameters.
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError>;
    
    /// Set training mode.
    fn set_training(&mut self, training: bool);
}

impl<T: Numeric> NormalizationLayer<T> for LayerNorm<T> {
    fn forward(&self, input: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError> {
        Layer::forward(self, input.clone())
    }
    
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        ParameterizedLayer::init_parameters(self, init_strategy)
    }
    
    fn set_training(&mut self, training: bool) {
        Layer::set_training(self, training);
    }
}

impl<T: Numeric> NormalizationLayer<T> for RMSNorm<T> {
    fn forward(&self, input: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError> {
        Layer::forward(self, input.clone())
    }
    
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        ParameterizedLayer::init_parameters(self, init_strategy)
    }
    
    fn set_training(&mut self, training: bool) {
        Layer::set_training(self, training);
    }
}

/// Create a normalization layer based on configuration.
pub fn create_normalization_layer<T: Numeric>(
    config: &EncoderLayerConfig,
) -> Result<Box<dyn NormalizationLayer<T>>, TensorError> {
    match config.normalization_type {
        NormalizationType::LayerNorm => {
            let norm_config = LayerNormConfig::new(vec![config.hidden_size])
                .with_eps(config.layer_norm_eps)
                .with_elementwise_affine(true);
            let layer = LayerNorm::new(norm_config)?;
            Ok(Box::new(layer))
        }
        NormalizationType::RMSNorm => {
            let norm_config = RMSNormConfig::new(vec![config.hidden_size])
                .with_eps(config.layer_norm_eps);
            let layer = RMSNorm::new(norm_config)?;
            Ok(Box::new(layer))
        }
        _ => Err(TensorError::UnsupportedOperation {
            operation: format!("Normalization type {:?}", config.normalization_type),
            context: Some(ErrorContext::new("create_normalization_layer", "encoder.rs")),
        }),
    }
}

/// Single Transformer encoder layer.
///
/// Implements the standard Transformer encoder layer with:
/// - Multi-head self-attention
/// - Position-wise feed-forward network
/// - Residual connections around each sub-layer
/// - Layer normalization (pre-norm or post-norm)
/// - Dropout for regularization
///
/// # Examples
///
/// ```rust
/// use qilin_inference::transformer::{TransformerEncoderLayer, EncoderLayerConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
/// use qilin_inference::layers::ParameterInit;
///
/// // Create encoder layer configuration
/// let config = EncoderLayerConfig {
///     hidden_size: 512,
///     num_attention_heads: 8,
///     intermediate_size: 2048,
///     dropout: 0.1,
///     attention_dropout: 0.1,
///     // ... other config fields
/// };
///
/// // Create and initialize encoder layer
/// let mut layer = TransformerEncoderLayer::new(config).unwrap();
/// layer.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![2, 10, 512]), 0.0, 1.0).unwrap();
/// let output = layer.forward(&input, None).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 10, 512]);
/// ```
#[derive(Debug)]
pub struct TransformerEncoderLayer<T: Numeric> {
    /// Multi-head self-attention layer
    self_attention: MultiHeadAttention<T>,
    /// Feed-forward network
    feed_forward: FeedForward<T>,
    /// First normalization layer (for attention)
    norm1: Box<dyn NormalizationLayer<T>>,
    /// Second normalization layer (for FFN)
    norm2: Box<dyn NormalizationLayer<T>>,
    /// Dropout layer
    dropout: Option<Dropout<T>>,
    /// Layer configuration
    config: EncoderLayerConfig,
    /// Training mode flag
    training: bool,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> TransformerEncoderLayer<T> {
    /// Create a new Transformer encoder layer.
    pub fn new(config: EncoderLayerConfig) -> Result<Self, TensorError> {
        // Create attention layer
        let attention_config = AttentionConfig::new(config.hidden_size, config.num_attention_heads)
            .with_dropout(config.attention_dropout)
            .with_bias(config.attention_bias);
        let self_attention = MultiHeadAttention::new(attention_config)?;
        
        // Create feed-forward network
        let ff_config = FeedForwardConfig::new(config.hidden_size, config.intermediate_size)
            .with_activation(convert_activation_type(config.activation_function))
            .with_bias(config.use_bias)
            .with_gated(config.use_gated_ffn);
        let feed_forward = FeedForward::new(ff_config)?;
        
        // Create normalization layers
        let norm1 = create_normalization_layer(&config)?;
        let norm2 = create_normalization_layer(&config)?;
        
        // Create dropout layer if needed
        let dropout = if config.dropout > 0.0 {
            let dropout_config = DropoutConfig::new(config.dropout);
            Some(Dropout::new(dropout_config)?)
        } else {
            None
        };
        
        Ok(Self {
            self_attention,
            feed_forward,
            norm1,
            norm2,
            dropout,
            config,
            training: true,
            _phantom: PhantomData,
        })
    }
    
    /// Initialize all parameters in the layer.
    pub fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        // Initialize attention parameters
        self.self_attention.init_parameters(init_strategy.clone())?;

        // Initialize feed-forward parameters
        self.feed_forward.init_parameters(init_strategy.clone())?;

        // Initialize normalization parameters
        self.norm1.init_parameters(init_strategy.clone())?;
        self.norm2.init_parameters(init_strategy)?;
        
        Ok(())
    }
    
    /// Set training mode for all sub-layers.
    pub fn set_training(&mut self, training: bool) {
        self.training = training;
        self.self_attention.set_training(training);
        self.feed_forward.set_training(training);
        self.norm1.set_training(training);
        self.norm2.set_training(training);
        if let Some(ref mut dropout) = self.dropout {
            dropout.set_training(training);
        }
    }
    
    /// Get the layer configuration.
    pub fn config(&self) -> &EncoderLayerConfig {
        &self.config
    }

    /// Forward pass through the encoder layer.
    ///
    /// # Arguments
    /// * `input` - Input tensor with shape [batch_size, seq_len, hidden_size]
    /// * `attention_mask` - Optional attention mask to prevent attention to certain positions
    ///
    /// # Returns
    /// Output tensor with the same shape as input
    pub fn forward(
        &self,
        input: &CpuTensor<T>,
        attention_mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        // Validate input shape
        if input.shape().dims().len() != 3 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, 0, self.config.hidden_size], // batch, seq_len, hidden_size
                actual: input.shape().dims().to_vec(),
                context: Some(ErrorContext::new("forward", "encoder.rs")),
            });
        }

        if input.shape().dims()[2] != self.config.hidden_size {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, 0, self.config.hidden_size],
                actual: input.shape().dims().to_vec(),
                context: Some(ErrorContext::new("forward", "encoder.rs")),
            });
        }

        let mut x = input.clone();

        // 1. Self-attention sub-layer
        let residual1 = x.clone();

        // Pre-normalization
        if self.config.pre_norm {
            x = self.norm1.forward(&x)?;
        }

        // Self-attention
        let (attention_output, _attention_weights) = self.self_attention.forward(&x, &x, &x, attention_mask)?;

        // Apply dropout if configured
        let attention_output = if let Some(ref dropout) = self.dropout {
            dropout.forward(attention_output)?
        } else {
            attention_output
        };

        // Residual connection
        x = residual1.add(&attention_output)?;

        // Post-normalization
        if !self.config.pre_norm {
            x = self.norm1.forward(&x)?;
        }

        // 2. Feed-forward sub-layer
        let residual2 = x.clone();

        // Pre-normalization
        if self.config.pre_norm {
            x = self.norm2.forward(&x)?;
        }

        // Feed-forward network
        let ff_output = self.feed_forward.forward(x)?;

        // Apply dropout if configured
        let ff_output = if let Some(ref dropout) = self.dropout {
            dropout.forward(ff_output)?
        } else {
            ff_output
        };

        // Residual connection
        x = residual2.add(&ff_output)?;

        // Post-normalization
        if !self.config.pre_norm {
            x = self.norm2.forward(&x)?;
        }

        Ok(x)
    }
}

/// Multi-layer Transformer encoder.
///
/// Stacks multiple TransformerEncoderLayer instances to form a complete encoder.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::transformer::{TransformerEncoder, TransformerConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
/// use qilin_inference::layers::ParameterInit;
///
/// // Create encoder from config
/// let config = TransformerConfig::bert_base();
/// let mut encoder = TransformerEncoder::new(&config).unwrap();
/// encoder.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![2, 128, 768]), 0.0, 1.0).unwrap();
/// let output = encoder.forward(&input, None).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 128, 768]);
/// ```
#[derive(Debug)]
pub struct TransformerEncoder<T: Numeric> {
    /// Stack of encoder layers
    layers: Vec<TransformerEncoderLayer<T>>,
    /// Optional final normalization layer
    final_norm: Option<Box<dyn NormalizationLayer<T>>>,
    /// Number of layers
    num_layers: usize,
    /// Training mode flag
    training: bool,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> TransformerEncoder<T> {
    /// Create a new Transformer encoder from configuration.
    pub fn new(config: &TransformerConfig) -> Result<Self, TensorError> {
        let layer_config = EncoderLayerConfig::from(config);
        let mut layers = Vec::with_capacity(config.num_layers);

        // Create encoder layers
        for _ in 0..config.num_layers {
            let layer = TransformerEncoderLayer::new(layer_config.clone())?;
            layers.push(layer);
        }

        // Create final normalization layer if using pre-norm
        let final_norm = if config.pre_norm {
            Some(create_normalization_layer(&layer_config)?)
        } else {
            None
        };

        Ok(Self {
            layers,
            final_norm,
            num_layers: config.num_layers,
            training: true,
            _phantom: PhantomData,
        })
    }

    /// Initialize parameters for all layers.
    pub fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        for layer in &mut self.layers {
            layer.init_parameters(init_strategy.clone())?;
        }

        if let Some(ref mut final_norm) = self.final_norm {
            final_norm.init_parameters(init_strategy)?;
        }

        Ok(())
    }

    /// Set training mode for all layers.
    pub fn set_training(&mut self, training: bool) {
        self.training = training;
        for layer in &mut self.layers {
            layer.set_training(training);
        }

        if let Some(ref mut final_norm) = self.final_norm {
            final_norm.set_training(training);
        }
    }

    /// Get the number of layers.
    pub fn num_layers(&self) -> usize {
        self.num_layers
    }

    /// Forward pass through all encoder layers.
    ///
    /// # Arguments
    /// * `input` - Input tensor with shape [batch_size, seq_len, hidden_size]
    /// * `attention_mask` - Optional attention mask
    ///
    /// # Returns
    /// Output tensor with the same shape as input
    pub fn forward(
        &self,
        input: &CpuTensor<T>,
        attention_mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        let mut x = input.clone();

        // Pass through each encoder layer
        for layer in &self.layers {
            x = layer.forward(&x, attention_mask)?;
        }

        // Apply final normalization if present
        if let Some(ref final_norm) = self.final_norm {
            x = final_norm.forward(&x)?;
        }

        Ok(x)
    }
}
