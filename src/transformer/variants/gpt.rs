//! GPT (Generative Pre-trained Transformer) implementation.
//!
//! This module provides a complete GPT model implementation using the core
//! Transformer components. GPT is a decoder-only autoregressive language model.

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, TensorFactory, Numeric, Shape, cpu::{CpuTensor, CpuTensorFactory}};
use crate::layers::{Layer, ParameterizedLayer, ParameterInit, embedding::{Embedding, EmbeddingConfig}};
use crate::error::{TensorError, ErrorContext};
use super::super::{
    config::{TransformerConfig, ModelType, ActivationType, NormalizationType, PositionEncodingType},
    block::TransformerBlock,
    position::{create_position_encoding, PositionEncodingConfig, utils::create_causal_mask},
};

/// Configuration specific to GPT models.
#[derive(Debug, Clone)]
pub struct GPTConfig {
    /// Base transformer configuration
    pub base_config: TransformerConfig,
    /// Whether to tie input and output embeddings
    pub tie_embeddings: bool,
    /// Whether to use bias in the final linear layer
    pub use_output_bias: bool,
    /// Scale factor for embeddings
    pub embedding_scale: Option<f32>,
}

impl GPTConfig {
    /// Create a new GPT configuration.
    pub fn new(vocab_size: usize, hidden_size: usize, num_layers: usize, num_attention_heads: usize) -> Self {
        let base_config = TransformerConfig::gpt_style(vocab_size, hidden_size, num_layers, num_attention_heads);
        
        Self {
            base_config,
            tie_embeddings: true,
            use_output_bias: false,
            embedding_scale: None,
        }
    }
    
    /// Create GPT-2 Small configuration (117M parameters).
    pub fn gpt2_small() -> Self {
        Self {
            base_config: TransformerConfig::gpt2_small(),
            tie_embeddings: true,
            use_output_bias: false,
            embedding_scale: None,
        }
    }
    
    /// Create GPT-2 Medium configuration (345M parameters).
    pub fn gpt2_medium() -> Self {
        Self {
            base_config: TransformerConfig::gpt2_medium(),
            tie_embeddings: true,
            use_output_bias: false,
            embedding_scale: None,
        }
    }
    
    /// Create GPT-2 Large configuration (762M parameters).
    pub fn gpt2_large() -> Self {
        Self {
            base_config: TransformerConfig::gpt2_large(),
            tie_embeddings: true,
            use_output_bias: false,
            embedding_scale: None,
        }
    }
    
    /// Create GPT-2 XL configuration (1.5B parameters).
    pub fn gpt2_xl() -> Self {
        Self {
            base_config: TransformerConfig::gpt2_xl(),
            tie_embeddings: true,
            use_output_bias: false,
            embedding_scale: None,
        }
    }
    
    /// Set whether to tie input and output embeddings.
    pub fn with_tied_embeddings(mut self, tie: bool) -> Self {
        self.tie_embeddings = tie;
        self
    }
    
    /// Set whether to use bias in output layer.
    pub fn with_output_bias(mut self, use_bias: bool) -> Self {
        self.use_output_bias = use_bias;
        self
    }
    
    /// Set embedding scale factor.
    pub fn with_embedding_scale(mut self, scale: f32) -> Self {
        self.embedding_scale = Some(scale);
        self
    }
}

/// GPT model implementation.
///
/// A complete GPT model with token embeddings, position encodings,
/// transformer layers, and language modeling head.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::transformer::variants::{GPTModel, GPTConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
/// use qilin_inference::layers::ParameterInit;
///
/// // Create GPT-2 Small model
/// let config = GPTConfig::gpt2_small();
/// let mut model = GPTModel::new(config).unwrap();
/// model.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass
/// let input_ids = CpuTensor::from_data(
///     vec![1, 2, 3, 4, 5],
///     Shape::new(vec![1, 5])
/// ).unwrap();
/// let logits = model.forward(&input_ids, None).unwrap();
/// assert_eq!(logits.shape().dims(), &[1, 5, 50257]); // vocab_size
/// ```
#[derive(Debug)]
pub struct GPTModel<T: Numeric> {
    /// Token embedding layer
    token_embeddings: Embedding<T>,
    /// Position encoding layer
    position_encoding: Box<dyn crate::transformer::position::PositionEncoding<T>>,
    /// Transformer block
    transformer: TransformerBlock<T>,
    /// Language modeling head (linear layer)
    lm_head: Option<CpuTensor<T>>, // Weight matrix for output projection
    /// Configuration
    config: GPTConfig,
    /// Training mode flag
    training: bool,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> GPTModel<T> {
    /// Create a new GPT model.
    pub fn new(config: GPTConfig) -> Result<Self, TensorError> {
        // Validate configuration
        if config.base_config.model_type != ModelType::DecoderOnly {
            return Err(TensorError::UnsupportedOperation {
                operation: format!("GPT requires decoder-only model type, got {:?}", config.base_config.model_type),
                context: Some(ErrorContext::new("new", "gpt.rs")),
            });
        }
        
        // Create token embeddings
        let embedding_config = EmbeddingConfig::new(config.base_config.vocab_size, config.base_config.hidden_size);
        let token_embeddings = Embedding::new(embedding_config)?;
        
        // Create position encoding
        let pos_config = PositionEncodingConfig::new(
            config.base_config.position_encoding_type,
            config.base_config.max_position_embeddings,
            config.base_config.hidden_size,
        );
        let position_encoding = create_position_encoding(&pos_config)?;
        
        // Create transformer block
        let transformer = TransformerBlock::new(config.base_config.clone())?;
        
        // Create language modeling head if not tying embeddings
        let lm_head = if !config.tie_embeddings {
            let lm_head_weight = CpuTensorFactory::zeros(&Shape::new(vec![
                config.base_config.hidden_size,
                config.base_config.vocab_size,
            ]))?;
            Some(lm_head_weight)
        } else {
            None
        };
        
        Ok(Self {
            token_embeddings,
            position_encoding,
            transformer,
            lm_head,
            config,
            training: true,
            _phantom: PhantomData,
        })
    }
    
    /// Initialize all parameters.
    pub fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        // Initialize token embeddings
        self.token_embeddings.init_parameters(init_strategy.clone())?;

        // Initialize transformer
        self.transformer.init_parameters(init_strategy.clone())?;
        
        // Initialize language modeling head if present
        if let Some(ref mut lm_head) = self.lm_head {
            match init_strategy {
                ParameterInit::Normal { mean, std } => {
                    *lm_head = CpuTensorFactory::randn(lm_head.shape(), T::from_f32(mean), T::from_f32(std))?;
                }
                _ => {
                    *lm_head = CpuTensorFactory::randn(lm_head.shape(), T::from_f32(0.0), T::from_f32(0.02))?;
                }
            }
        }
        
        Ok(())
    }
    
    /// Set training mode.
    pub fn set_training(&mut self, training: bool) {
        self.training = training;
        self.token_embeddings.set_training(training);
        self.transformer.set_training(training);
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &GPTConfig {
        &self.config
    }
    
    /// Forward pass through the GPT model.
    ///
    /// # Arguments
    /// * `input_ids` - Token IDs with shape [batch_size, seq_len]
    /// * `attention_mask` - Optional attention mask
    ///
    /// # Returns
    /// Logits tensor with shape [batch_size, seq_len, vocab_size]
    pub fn forward(
        &self,
        input_ids: &CpuTensor<usize>,
        attention_mask: Option<&CpuTensor<usize>>,
    ) -> Result<CpuTensor<T>, TensorError> {
        let input_shape = input_ids.shape();
        if input_shape.dims().len() != 2 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, 0], // batch_size, seq_len
                actual: input_shape.dims().to_vec(),
                context: Some(ErrorContext::new("forward", "gpt.rs")),
            });
        }
        
        let batch_size = input_shape.dims()[0];
        let seq_len = input_shape.dims()[1];
        
        // 1. Token embeddings
        let input_tokens: Vec<usize> = input_ids.data().to_vec();
        let mut hidden_states = self.token_embeddings.forward(input_tokens)?;
        
        // 2. Apply embedding scaling if configured
        if let Some(scale) = self.config.embedding_scale {
            hidden_states = hidden_states.mul_scalar(T::from_f32(scale))?;
        }
        
        // 3. Add position encodings
        hidden_states = self.position_encoding.encode(&hidden_states, None)?;
        
        // 4. Create causal attention mask
        let causal_mask = create_causal_mask::<T>(seq_len)?;
        
        // 5. Pass through transformer
        hidden_states = self.transformer.forward(&hidden_states, None, Some(&causal_mask), None)?;
        
        // 6. Language modeling head
        let logits = if let Some(ref lm_head) = self.lm_head {
            // Use separate LM head
            self.apply_lm_head(&hidden_states, lm_head)?
        } else {
            // Tie embeddings: use transposed token embedding weights
            self.apply_tied_lm_head(&hidden_states)?
        };
        
        Ok(logits)
    }
    
    /// Apply language modeling head using separate weights.
    fn apply_lm_head(&self, hidden_states: &CpuTensor<T>, lm_head: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError> {
        // hidden_states: [batch_size, seq_len, hidden_size]
        // lm_head: [hidden_size, vocab_size]
        // output: [batch_size, seq_len, vocab_size]
        
        // This is a simplified implementation
        // In practice, you'd implement proper matrix multiplication
        hidden_states.matmul(lm_head)
    }
    
    /// Apply language modeling head using tied embeddings.
    fn apply_tied_lm_head(&self, hidden_states: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError> {
        // Get embedding weights and transpose them
        let embedding_weights = self.token_embeddings.weight();
        
        // Apply matrix multiplication with transposed embeddings
        self.apply_lm_head(hidden_states, embedding_weights)
    }
    
    /// Generate text autoregressively.
    pub fn generate(
        &self,
        input_ids: &CpuTensor<usize>,
        max_length: usize,
        temperature: f32,
        top_k: Option<usize>,
        top_p: Option<f32>,
    ) -> Result<CpuTensor<usize>, TensorError> {
        // This is a placeholder implementation
        // In practice, you'd implement proper autoregressive generation
        // with sampling strategies (temperature, top-k, top-p, etc.)
        
        Ok(input_ids.clone())
    }
}
