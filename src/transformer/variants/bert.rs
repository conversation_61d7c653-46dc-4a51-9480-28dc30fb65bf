//! BERT (Bidirectional Encoder Representations from Transformers) implementation.
//!
//! This module provides a complete BERT model implementation using the core
//! Transformer components. BERT is an encoder-only bidirectional language model.

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, TensorFactory, Numeric, Shape, cpu::{CpuTensor, CpuTensorFactory}};
use crate::layers::{Layer, ParameterizedLayer, ParameterInit, embedding::{Embedding, EmbeddingConfig}};
use crate::error::{TensorError, ErrorContext};
use super::super::{
    config::{TransformerConfig, ModelType, ActivationType, NormalizationType, PositionEncodingType},
    block::TransformerBlock,
    position::{create_position_encoding, PositionEncodingConfig, utils::create_padding_mask},
};

/// Configuration specific to BERT models.
#[derive(Debug, Clone)]
pub struct BERTConfig {
    /// Base transformer configuration
    pub base_config: TransformerConfig,
    /// Number of token types (for segment embeddings)
    pub type_vocab_size: usize,
    /// Whether to use pooler layer
    pub use_pooler: bool,
    /// Pooler activation function
    pub pooler_activation: ActivationType,
    /// Whether to use NSP (Next Sentence Prediction) head
    pub use_nsp_head: bool,
    /// Whether to use MLM (Masked Language Modeling) head
    pub use_mlm_head: bool,
}

impl BERTConfig {
    /// Create a new BERT configuration.
    pub fn new(vocab_size: usize, hidden_size: usize, num_layers: usize, num_attention_heads: usize) -> Self {
        let base_config = TransformerConfig::bert_style(vocab_size, hidden_size, num_layers, num_attention_heads);
        
        Self {
            base_config,
            type_vocab_size: 2, // [SEP] and [CLS] token types
            use_pooler: true,
            pooler_activation: ActivationType::Tanh,
            use_nsp_head: true,
            use_mlm_head: true,
        }
    }
    
    /// Create BERT Base configuration (110M parameters).
    pub fn bert_base() -> Self {
        Self {
            base_config: TransformerConfig::bert_base(),
            type_vocab_size: 2,
            use_pooler: true,
            pooler_activation: ActivationType::Tanh,
            use_nsp_head: true,
            use_mlm_head: true,
        }
    }
    
    /// Create BERT Large configuration (340M parameters).
    pub fn bert_large() -> Self {
        Self {
            base_config: TransformerConfig::bert_large(),
            type_vocab_size: 2,
            use_pooler: true,
            pooler_activation: ActivationType::Tanh,
            use_nsp_head: true,
            use_mlm_head: true,
        }
    }
    
    /// Set the number of token types.
    pub fn with_type_vocab_size(mut self, size: usize) -> Self {
        self.type_vocab_size = size;
        self
    }
    
    /// Set whether to use pooler.
    pub fn with_pooler(mut self, use_pooler: bool) -> Self {
        self.use_pooler = use_pooler;
        self
    }
    
    /// Set pooler activation function.
    pub fn with_pooler_activation(mut self, activation: ActivationType) -> Self {
        self.pooler_activation = activation;
        self
    }
}

/// BERT model implementation.
///
/// A complete BERT model with token embeddings, position encodings, token type embeddings,
/// transformer layers, and optional task-specific heads.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::transformer::variants::{BERTModel, BERTConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
/// use qilin_inference::layers::ParameterInit;
///
/// // Create BERT Base model
/// let config = BERTConfig::bert_base();
/// let mut model = BERTModel::new(config).unwrap();
/// model.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass
/// let input_ids = CpuTensor::from_data(
///     vec![101, 2023, 2003, 1037, 3231, 102], // [CLS] this is a test [SEP]
///     Shape::new(vec![1, 6])
/// ).unwrap();
/// let outputs = model.forward(&input_ids, None, None, None).unwrap();
/// ```
#[derive(Debug)]
pub struct BERTModel<T: Numeric> {
    /// Token embedding layer
    token_embeddings: Embedding<T>,
    /// Position encoding layer
    position_encoding: Box<dyn crate::transformer::position::PositionEncoding<T>>,
    /// Token type embedding layer
    token_type_embeddings: Embedding<T>,
    /// Transformer block
    transformer: TransformerBlock<T>,
    /// Pooler layer (for [CLS] token)
    pooler: Option<CpuTensor<T>>, // Linear layer weights
    /// MLM head weights
    mlm_head: Option<CpuTensor<T>>,
    /// NSP head weights
    nsp_head: Option<CpuTensor<T>>,
    /// Configuration
    config: BERTConfig,
    /// Training mode flag
    training: bool,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> BERTModel<T> {
    /// Create a new BERT model.
    pub fn new(config: BERTConfig) -> Result<Self, TensorError> {
        // Validate configuration
        if config.base_config.model_type != ModelType::EncoderOnly {
            return Err(TensorError::UnsupportedOperation {
                operation: format!("BERT requires encoder-only model type, got {:?}", config.base_config.model_type),
                context: Some(ErrorContext::new("new", "bert.rs")),
            });
        }
        
        // Create token embeddings
        let token_embedding_config = EmbeddingConfig::new(config.base_config.vocab_size, config.base_config.hidden_size);
        let token_embeddings = Embedding::new(token_embedding_config)?;
        
        // Create position encoding
        let pos_config = PositionEncodingConfig::new(
            config.base_config.position_encoding_type,
            config.base_config.max_position_embeddings,
            config.base_config.hidden_size,
        );
        let position_encoding = create_position_encoding(&pos_config)?;
        
        // Create token type embeddings
        let type_embedding_config = EmbeddingConfig::new(config.type_vocab_size, config.base_config.hidden_size);
        let token_type_embeddings = Embedding::new(type_embedding_config)?;
        
        // Create transformer block
        let transformer = TransformerBlock::new(config.base_config.clone())?;
        
        // Create pooler if enabled
        let pooler = if config.use_pooler {
            let pooler_weight = CpuTensorFactory::zeros(&Shape::new(vec![
                config.base_config.hidden_size,
                config.base_config.hidden_size,
            ]))?;
            Some(pooler_weight)
        } else {
            None
        };
        
        // Create MLM head if enabled
        let mlm_head = if config.use_mlm_head {
            let mlm_weight = CpuTensorFactory::zeros(&Shape::new(vec![
                config.base_config.hidden_size,
                config.base_config.vocab_size,
            ]))?;
            Some(mlm_weight)
        } else {
            None
        };
        
        // Create NSP head if enabled
        let nsp_head = if config.use_nsp_head {
            let nsp_weight = CpuTensorFactory::zeros(&Shape::new(vec![
                config.base_config.hidden_size,
                2, // Binary classification
            ]))?;
            Some(nsp_weight)
        } else {
            None
        };
        
        Ok(Self {
            token_embeddings,
            position_encoding,
            token_type_embeddings,
            transformer,
            pooler,
            mlm_head,
            nsp_head,
            config,
            training: true,
            _phantom: PhantomData,
        })
    }
    
    /// Initialize all parameters.
    pub fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), TensorError> {
        // Initialize embeddings
        self.token_embeddings.init_parameters(init_strategy.clone())?;
        self.token_type_embeddings.init_parameters(init_strategy.clone())?;

        // Initialize transformer
        self.transformer.init_parameters(init_strategy.clone())?;
        
        // Initialize heads
        if let Some(ref mut pooler) = self.pooler {
            *pooler = CpuTensorFactory::randn(pooler.shape(), T::from_f32(0.0), T::from_f32(0.02))?;
        }

        if let Some(ref mut mlm_head) = self.mlm_head {
            *mlm_head = CpuTensorFactory::randn(mlm_head.shape(), T::from_f32(0.0), T::from_f32(0.02))?;
        }

        if let Some(ref mut nsp_head) = self.nsp_head {
            *nsp_head = CpuTensorFactory::randn(nsp_head.shape(), T::from_f32(0.0), T::from_f32(0.02))?;
        }
        
        Ok(())
    }
    
    /// Set training mode.
    pub fn set_training(&mut self, training: bool) {
        self.training = training;
        self.token_embeddings.set_training(training);
        self.token_type_embeddings.set_training(training);
        self.transformer.set_training(training);
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &BERTConfig {
        &self.config
    }
    
    /// Forward pass through the BERT model.
    ///
    /// # Arguments
    /// * `input_ids` - Token IDs with shape [batch_size, seq_len]
    /// * `token_type_ids` - Optional token type IDs for segment embeddings
    /// * `attention_mask` - Optional attention mask
    /// * `position_ids` - Optional position IDs
    ///
    /// # Returns
    /// BERTOutput containing sequence output, pooled output, and task-specific outputs
    pub fn forward(
        &self,
        input_ids: &CpuTensor<usize>,
        token_type_ids: Option<&CpuTensor<usize>>,
        attention_mask: Option<&CpuTensor<usize>>,
        position_ids: Option<&CpuTensor<usize>>,
    ) -> Result<BERTOutput<T>, TensorError> {
        let input_shape = input_ids.shape();
        if input_shape.dims().len() != 2 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, 0], // batch_size, seq_len
                actual: input_shape.dims().to_vec(),
                context: Some(ErrorContext::new("forward", "bert.rs")),
            });
        }
        
        let batch_size = input_shape.dims()[0];
        let seq_len = input_shape.dims()[1];
        
        // 1. Token embeddings
        let input_tokens: Vec<usize> = input_ids.data().to_vec();
        let mut hidden_states = self.token_embeddings.forward(input_tokens)?;

        // 2. Add position encodings
        hidden_states = self.position_encoding.encode(&hidden_states, position_ids)?;

        // 3. Add token type embeddings
        if let Some(type_ids) = token_type_ids {
            let type_tokens: Vec<usize> = type_ids.data().to_vec();
            let type_embeddings = self.token_type_embeddings.forward(type_tokens)?;
            hidden_states = hidden_states.add(&type_embeddings)?;
        } else {
            // Use default token type 0 for all tokens
            let default_type_tokens = vec![0; batch_size * seq_len];
            let type_embeddings = self.token_type_embeddings.forward(default_type_tokens)?;
            hidden_states = hidden_states.add(&type_embeddings)?;
        }
        
        // 4. Create attention mask
        let attention_mask_tensor = if let Some(mask) = attention_mask {
            Some(create_padding_mask::<T>(mask)?)
        } else {
            None
        };
        
        // 5. Pass through transformer
        let sequence_output = self.transformer.forward(&hidden_states, None, attention_mask_tensor.as_ref(), None)?;
        
        // 6. Pooler output (from [CLS] token)
        let pooled_output = if let Some(ref pooler) = self.pooler {
            let cls_token = sequence_output.slice(&[0..batch_size, 0..1, 0..self.config.base_config.hidden_size])?; // [batch_size, 1, hidden_size]
            let pooled = self.apply_pooler(&cls_token, pooler)?;
            Some(pooled)
        } else {
            None
        };
        
        // 7. Task-specific heads
        let mlm_logits = if let Some(ref mlm_head) = self.mlm_head {
            Some(self.apply_mlm_head(&sequence_output, mlm_head)?)
        } else {
            None
        };
        
        let nsp_logits = if let (Some(ref nsp_head), Some(ref pooled)) = (&self.nsp_head, &pooled_output) {
            Some(self.apply_nsp_head(pooled, nsp_head)?)
        } else {
            None
        };
        
        Ok(BERTOutput {
            sequence_output,
            pooled_output,
            mlm_logits,
            nsp_logits,
        })
    }
    
    /// Apply pooler to [CLS] token.
    fn apply_pooler(&self, cls_token: &CpuTensor<T>, pooler: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError> {
        // Apply linear transformation and activation
        let pooled = cls_token.matmul(pooler)?;
        
        // Apply activation (simplified - would need proper activation implementation)
        Ok(pooled)
    }
    
    /// Apply MLM head.
    fn apply_mlm_head(&self, hidden_states: &CpuTensor<T>, mlm_head: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError> {
        hidden_states.matmul(mlm_head)
    }
    
    /// Apply NSP head.
    fn apply_nsp_head(&self, pooled_output: &CpuTensor<T>, nsp_head: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError> {
        pooled_output.matmul(nsp_head)
    }
}

/// BERT model output.
#[derive(Debug)]
pub struct BERTOutput<T: Numeric> {
    /// Sequence output from transformer layers [batch_size, seq_len, hidden_size]
    pub sequence_output: CpuTensor<T>,
    /// Pooled output from [CLS] token [batch_size, hidden_size]
    pub pooled_output: Option<CpuTensor<T>>,
    /// MLM logits [batch_size, seq_len, vocab_size]
    pub mlm_logits: Option<CpuTensor<T>>,
    /// NSP logits [batch_size, 2]
    pub nsp_logits: Option<CpuTensor<T>>,
}
