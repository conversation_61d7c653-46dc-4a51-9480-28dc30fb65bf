[package]
name = "qilin-inference"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "Qilin - A high-performance Rust-based deep learning inference engine for Transformer architectures"
license = "MIT OR Apache-2.0"
repository = "https://github.com/yourusername/qilin-inference"
keywords = ["deep-learning", "transformer", "inference", "neural-networks", "rust"]
categories = ["science", "algorithms"]
readme = "README.md"

[lib]
name = "qilin_inference"
crate-type = ["cdylib", "rlib"]

[features]
default = ["cpu", "f32"]
cpu = []
cuda = ["cudarc"]
metal = []
f16 = ["half"]
f32 = []
f64 = []
simd = ["wide"]
python-bindings = ["pyo3"]
benchmarks = ["criterion"]

[dependencies]
# Core dependencies
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
log = "0.4"
env_logger = "0.10"

# Numerical computing
ndarray = { version = "0.15", features = ["serde"] }
num-traits = "0.2"
half = { version = "2.3", optional = true }
rand = "0.8"

# Parallel computing
rayon = "1.8"
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"

# SIMD optimization
wide = { version = "0.7", optional = true }

# Memory management
memmap2 = "0.9"

# GPU support (optional)
cudarc = { version = "0.10", optional = true }

# Python bindings (optional)
pyo3 = { version = "0.20", features = ["extension-module"], optional = true }

# Model loading and serialization
zip = "0.6"
flate2 = "1.0"
toml = "0.8"
chrono = { version = "0.4", features = ["serde"] }

# Utilities
uuid = { version = "1.6", features = ["v4"] }
once_cell = "1.19"
parking_lot = "0.12"
num_cpus = "1.16"

# Benchmarking (optional)
criterion = { version = "0.5", features = ["html_reports"], optional = true }

[dev-dependencies]
proptest = "1.4"
tempfile = "3.8"
approx = "0.5"
fastrand = "2.0"

[build-dependencies]
cc = "1.0"

[[bench]]
name = "tensor_ops"
harness = false
required-features = ["benchmarks"]

[[bench]]
name = "attention"
harness = false
required-features = ["benchmarks"]

[[bench]]
name = "positional_encoding_bench"
harness = false
required-features = ["benchmarks"]

[[bench]]
name = "inference"
harness = false
required-features = ["benchmarks"]

[[bench]]
name = "attention_benchmarks"
harness = false
required-features = ["benchmarks"]

[[bench]]
name = "neural_network_layers"
harness = false
required-features = ["benchmarks"]

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.bench]
opt-level = 3
debug = true
