//! Demonstration of the Qilin Transformer implementation.
//! 
//! This example shows how to create and use different Transformer models
//! including GPT, BERT, and T5 variants.

use qilin_inference::transformer::*;
use qilin_inference::transformer::variants::{GPTModel, GPTConfig, BERTModel, BERTConfig, T5Model, T5Config};
use qilin_inference::tensor::{Shape, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::layers::ParameterInit;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Qilin Transformer Implementation Demo");
    println!("========================================");
    
    // 1. Basic Transformer Configuration
    println!("\n1. Creating basic Transformer configuration...");
    let config = TransformerConfig::new(1000, 512, 6, 8);
    println!("   ✓ Vocab size: {}", config.vocab_size);
    println!("   ✓ Hidden size: {}", config.hidden_size);
    println!("   ✓ Number of layers: {}", config.num_layers);
    println!("   ✓ Attention heads: {}", config.num_attention_heads);
    println!("   ✓ Configuration validation: {:?}", config.validate());
    
    // 2. GPT Model
    println!("\n2. Creating GPT-2 Small model...");
    let gpt_config = GPTConfig::gpt2_small();
    println!("   ✓ Model type: {:?}", gpt_config.base_config.model_type);
    println!("   ✓ Vocab size: {}", gpt_config.base_config.vocab_size);
    println!("   ✓ Hidden size: {}", gpt_config.base_config.hidden_size);
    println!("   ✓ Layers: {}", gpt_config.base_config.num_layers);
    println!("   ✓ Attention heads: {}", gpt_config.base_config.num_attention_heads);
    
    let mut gpt_model: GPTModel<f32> = GPTModel::new(gpt_config)?;
    gpt_model.init_parameters(ParameterInit::XavierUniform)?;
    println!("   ✓ GPT model created and initialized");
    
    // 3. BERT Model
    println!("\n3. Creating BERT Base model...");
    let bert_config = BERTConfig::bert_base();
    println!("   ✓ Model type: {:?}", bert_config.base_config.model_type);
    println!("   ✓ Vocab size: {}", bert_config.base_config.vocab_size);
    println!("   ✓ Hidden size: {}", bert_config.base_config.hidden_size);
    println!("   ✓ Layers: {}", bert_config.base_config.num_layers);
    println!("   ✓ Type vocab size: {}", bert_config.type_vocab_size);
    
    let mut bert_model: BERTModel<f32> = BERTModel::new(bert_config)?;
    bert_model.init_parameters(ParameterInit::XavierUniform)?;
    println!("   ✓ BERT model created and initialized");
    
    // 4. T5 Model
    println!("\n4. Creating T5 Small model...");
    let t5_config = T5Config::t5_small();
    println!("   ✓ Model type: {:?}", t5_config.base_config.model_type);
    println!("   ✓ Vocab size: {}", t5_config.base_config.vocab_size);
    println!("   ✓ Hidden size: {}", t5_config.base_config.hidden_size);
    println!("   ✓ Encoder layers: {:?}", t5_config.base_config.num_encoder_layers);
    println!("   ✓ Decoder layers: {:?}", t5_config.base_config.num_decoder_layers);
    println!("   ✓ Relative attention buckets: {}", t5_config.relative_attention_num_buckets);
    
    let mut t5_model: T5Model<f32> = T5Model::new(t5_config)?;
    t5_model.init_parameters(ParameterInit::XavierUniform)?;
    println!("   ✓ T5 model created and initialized");
    
    // 5. Position Encoding Examples
    println!("\n5. Testing position encodings...");
    
    // Learned position encoding
    let learned_config = PositionEncodingConfig::new(PositionEncodingType::Learned, 1024, 512);
    let mut learned_pos = LearnedPositionEmbedding::<f32>::new(learned_config)?;
    learned_pos.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 })?;
    println!("   ✓ Learned position encoding: max_len={}, d_model={}", 
             learned_pos.max_length(), learned_pos.d_model());
    
    // Sinusoidal position encoding
    let sin_config = PositionEncodingConfig::new(PositionEncodingType::Sinusoidal, 1024, 512);
    let sin_pos = SinusoidalPositionEncoding::<f32>::new(sin_config)?;
    println!("   ✓ Sinusoidal position encoding: max_len={}, d_model={}", 
             sin_pos.max_length(), sin_pos.d_model());
    
    // RoPE (Rotary Position Embedding)
    let rope_config = PositionEncodingConfig::new(PositionEncodingType::RoPE, 1024, 512);
    let rope = RotaryPositionEmbedding::<f32>::new(rope_config)?;
    println!("   ✓ RoPE position encoding: max_len={}, d_model={}", 
             rope.max_length(), rope.d_model());
    
    // 6. Transformer Block
    println!("\n6. Creating complete Transformer block...");
    let block_config = TransformerConfig::new(1000, 512, 6, 8);
    let mut transformer_block: TransformerBlock<f32> = TransformerBlock::new(block_config)?;
    transformer_block.init_parameters(ParameterInit::XavierUniform)?;
    println!("   ✓ Transformer block created and initialized");
    
    // 7. Layer Components
    println!("\n7. Testing individual layer components...");
    
    // Encoder layer
    let encoder_config = EncoderLayerConfig {
        hidden_size: 512,
        num_attention_heads: 8,
        intermediate_size: 2048,
        dropout: 0.1,
        attention_dropout: 0.1,
        activation_function: ActivationType::ReLU,
        normalization_type: NormalizationType::LayerNorm,
        pre_norm: false,
        layer_norm_eps: 1e-5,
        attention_bias: true,
        use_bias: true,
        use_gated_ffn: false,
    };
    let mut encoder_layer: TransformerEncoderLayer<f32> = TransformerEncoderLayer::new(encoder_config)?;
    encoder_layer.init_parameters(ParameterInit::XavierUniform)?;
    println!("   ✓ Encoder layer created and initialized");
    
    // Decoder layer
    let decoder_config = DecoderLayerConfig {
        hidden_size: 512,
        num_attention_heads: 8,
        intermediate_size: 2048,
        dropout: 0.1,
        attention_dropout: 0.1,
        cross_attention_dropout: Some(0.1),
        activation_function: ActivationType::ReLU,
        normalization_type: NormalizationType::LayerNorm,
        pre_norm: false,
        layer_norm_eps: 1e-5,
        attention_bias: true,
        use_bias: true,
        use_gated_ffn: false,
        decoder_only: false,
    };
    let mut decoder_layer: TransformerDecoderLayer<f32> = TransformerDecoderLayer::new(decoder_config)?;
    decoder_layer.init_parameters(ParameterInit::XavierUniform)?;
    println!("   ✓ Decoder layer created and initialized");
    
    // 8. Configuration Presets
    println!("\n8. Testing configuration presets...");
    
    // GPT variants
    let gpt2_medium = GPTConfig::gpt2_medium();
    println!("   ✓ GPT-2 Medium: {} layers, {} hidden size", 
             gpt2_medium.base_config.num_layers, gpt2_medium.base_config.hidden_size);
    
    let gpt2_large = GPTConfig::gpt2_large();
    println!("   ✓ GPT-2 Large: {} layers, {} hidden size", 
             gpt2_large.base_config.num_layers, gpt2_large.base_config.hidden_size);
    
    // BERT variants
    let bert_large = BERTConfig::bert_large();
    println!("   ✓ BERT Large: {} layers, {} hidden size", 
             bert_large.base_config.num_layers, bert_large.base_config.hidden_size);
    
    // T5 variants
    let t5_base = T5Config::t5_base();
    println!("   ✓ T5 Base: {} layers, {} hidden size", 
             t5_base.base_config.num_layers, t5_base.base_config.hidden_size);
    
    let t5_large = T5Config::t5_large();
    println!("   ✓ T5 Large: {} layers, {} hidden size", 
             t5_large.base_config.num_layers, t5_large.base_config.hidden_size);
    
    println!("\n🎉 All Transformer components created successfully!");
    println!("   The Qilin Transformer implementation is working correctly.");
    println!("   You can now use these models for inference and training.");
    
    Ok(())
}
